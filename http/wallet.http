# 环境变量配置
@baseUrlForLogin = http://paydev:8080
@baseUrl = http://localhost:8080
@tenantId = K99999999
@clientId = e5cd7e4891bf95d1d19206ce24a7b32e
@grantType = password
@username = admin
@password = admin123

### 查询每个钱包最新记录
GET {{baseUrl}}/wallet/coinrec/latest/page?chainType=tron&tokenSymbol=usdt&pageNum=1&pageSize=10
Authorization: Bearer {{access_token}}
clientid: {{clientId}}


<> 2025-06-20T162457.200.txt
<> 2025-06-20T162155.200.txt
<> 2025-06-20T161624.200.txt
<> 2025-06-20T161427.200.json
<> 2025-06-20T161416.200.json

### collect-tron
POST {{baseUrlL}}/wallet/coinrec/collect
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

{
    "fromAddress": "TRy4wLctZfdRikaf1fsk7vsbTMWFrBcgvZ",
    "chainType": "TRON",
    "tokenSymbol": "USDC",
    "amount": 600
}


### collect-bsc
POST {{baseUrl}}/wallet/coinrec/collect
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

{
    "fromAddress": "******************************************",
    "chainType": "BSC",
    "tokenSymbol": "USDT",
    "amount": 10
}

### collect-solana
POST {{baseUrl}}/wallet/transfer/collect
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

{
    "fromAddress": "4CPiGUw9MxGvt2qPTVnuwYqUBvkeh7atZbeKcNGNMAe9",
    "chainType": "SOLANA",
    "tokenSymbol": "USDT",
    "amount": 100000
}

### rec
POST {{baseUrl}}/wallet/coinrec/scan/TTuhNkJvT8vWBHjHpavwKAEMVemZK6tMKM/TRON
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}


### 查询资金钱包余额
GET {{baseUrl}}/wallet/transfer/getCapitalWallet?chain=TRON
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### withdraw-select
GET {{baseUrl}}/aset/payCoin/listForWithdrawals?pageSize=50&pageNum=1&txnCode=c1010&txnStatus=2
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### withdraw-selectSum
GET {{baseUrl}}/aset/payCoin/sumWithdrawalAmount
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### withdraw new
POST {{baseUrl}}/aset/payCoin/executeBatchWithdrawal
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

[
    26140,26036
]

### withdraw
POST {{baseUrl}}/wallet/transfer/withdraw
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

[
    {
        "requestId": 24104,
        "toAddress": "TP98LnUSEggNgh38nfs4mRaMTjkPtGCufE",
        "amount": 10.50,
        "tokenSymbol": "USDT",
        "chainType": "TRON",
        "memo": "TRON USDT提款"
    },
    {
        "requestId": 2411212,
        "toAddress": "******************************************",
        "amount": 1,
        "tokenSymbol": "USDT",
        "chainType": "BSC",
        "memo": "USDT BNB提款"
    }
]

### 手动交易补偿evm
POST {{baseUrl}}/wallet/scan/manual/evm/BSC/tx/0x3def7c6aa96111338a9777f3c93fc072df54a6e74784634826df185dfdf16660
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 手动交易补偿tron
POST {{baseUrl}}/wallet/scan/manual/tron/tx/ab330c20577e9e885cbd4d37fc430ad09ce6545a7fd390fe614f6134b2f05d7c
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

# ========================== Solana监控管理API ==========================

### 获取Solana监控状态
GET {{baseUrl}}/api/solana/monitor/status
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 获取Solana订阅信息列表
GET {{baseUrl}}/api/solana/monitor/subscriptions
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 手动触发Solana重连
POST {{baseUrl}}/api/solana/monitor/reconnect
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 获取Solana监控摘要
GET {{baseUrl}}/api/solana/monitor/summary
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

