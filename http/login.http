# ========================== HTTP Client 自动登录脚本 ==========================

# 环境变量配置
@baseUrlForLogin = http://paydev:8080
@baseUrl = http://localhost:8080
#@baseUrl = http://paydev:8080
#@baseUrl = http://paytest:8080
@tenantId = K99999999
@clientId = e5cd7e4891bf95d1d19206ce24a7b32e
@grantType = password
@username = admin
@password = admin123


### 1. login Token (明文请求 - 需要关闭API加密)
POST {{baseUrlForLogin}}/auth/login
Content-Type: application/json

{
  "tenantId": "{{tenantId}}",
  "clientId": "{{clientId}}",
  "grantType": "{{grantType}}",
  "username": "{{username}}",
  "password": "{{password}}"
}

> {%
    // 自动提取并存储访问令牌
    if (response.status === 200) {
        const responseBody = response.body;
        if (responseBody.code === 200 && responseBody.data && responseBody.data.access_token) {
            client.global.set("access_token", responseBody.data.access_token);
            client.global.set("refresh_token", responseBody.data.refresh_token);
            client.global.set("expire_in", responseBody.data.expire_in);
            console.log("登录成功，Token已保存");
            console.log("Access Token: " + responseBody.data.access_token);
            console.log("Token有效期: " + responseBody.data.expire_in + "秒");
        } else {
            console.log("登录失败: " + responseBody.msg);
        }
    } else {
        console.log("登录请求失败，HTTP状态码: " + response.status);
    }
%}

### 1.1 切换租户
GET {{baseUrlForLogin}}/system/tenant/dynamic/K99999999
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}


### 2. 使用Token刷新访问令牌（当令牌过期时使用）
POST {{baseUrlForLogin}}/auth/refresh
Content-Type: application/json

{
  "clientId": "{{clientId}}",
  "refreshToken": "{{refresh_token}}"
}

> {%
    // 自动更新访问令牌
    if (response.status === 200) {
        const responseBody = response.body;
        if (responseBody.code === 200 && responseBody.data && responseBody.data.access_token) {
            client.global.set("access_token", responseBody.data.access_token);
            client.global.set("expire_in", responseBody.data.expire_in);
            console.log("Token刷新成功");
        }
    }
%}

### 3. 测试Token有效性
GET {{baseUrl}}/system/user/profile
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

<> 2025-06-20T161424.200.json
<> 2025-06-20T152339.200.json
<> 2025-06-19T204055.200.json
<> 2025-06-19T202243.200.json
<> 2025-06-19T201920.200.txt



# 使用说明：
# 1. 首先执行 "用户登录获取Token" 请求，系统会自动保存access_token
# 2. 后续所有业务请求都会自动使用 {{access_token}} 和 {{clientId}} 变量
# 3. 当token过期时，执行 "刷新访问令牌" 请求更新token
# 4. 所有token相关变量都会自动保存在全局环境中
# 5. 注意：业务请求必须同时包含Authorization和clientid请求头才能通过验证
#
# 环境变量说明：
# - baseUrl: 服务器基础地址
# - tenantId: 租户ID（默认000000）
# - clientId: OAuth2客户端ID
# - username/password: 登录凭据
# - access_token: 自动保存的访问令牌
# - refresh_token: 自动保存的刷新令牌
#
#
