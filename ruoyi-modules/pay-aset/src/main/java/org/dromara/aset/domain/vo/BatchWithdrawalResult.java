package org.dromara.aset.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 批量提款结果响应对象
 *
 * <p>用于封装批量提款操作的执行结果，包含成功/失败统计和详细信息</p>
 *
 * <AUTHOR>
 * @date 2025/8/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchWithdrawalResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总处理数量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failureCount;

    /**
     * 成功的提款记录列表
     */
    private List<BatchWithdrawalItem> successItems;

    /**
     * 失败的提款记录列表
     */
    private List<BatchWithdrawalItem> failureItems;

    /**
     * 执行结果汇总信息
     */
    private String summary;

    /**
     * 批量提款项目详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchWithdrawalItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 交易ID
         */
        private Long txnId;

        /**
         * 成功时的结果信息（如交易哈希等）
         */
        private String result;

        /**
         * 失败时的错误信息
         */
        private String errorMessage;

        /**
         * 提款金额（用于显示）
         */
        private String amount;

        /**
         * 收款地址（用于显示）
         */
        private String toAddress;

        /**
         * 代币类型（用于显示）
         */
        private String tokenSymbol;

        /**
         * 创建成功项目
         */
        public static BatchWithdrawalItem success(Long txnId, String result, String amount, 
                                                String toAddress, String tokenSymbol) {
            return BatchWithdrawalItem.builder()
                .txnId(txnId)
                .result(result)
                .amount(amount)
                .toAddress(toAddress)
                .tokenSymbol(tokenSymbol)
                .build();
        }

        /**
         * 创建失败项目
         */
        public static BatchWithdrawalItem failure(Long txnId, String errorMessage, String amount, 
                                                String toAddress, String tokenSymbol) {
            return BatchWithdrawalItem.builder()
                .txnId(txnId)
                .errorMessage(errorMessage)
                .amount(amount)
                .toAddress(toAddress)
                .tokenSymbol(tokenSymbol)
                .build();
        }
    }

    /**
     * 创建批量提款结果
     */
    public static BatchWithdrawalResult create(List<BatchWithdrawalItem> successItems, 
                                             List<BatchWithdrawalItem> failureItems) {
        int totalCount = successItems.size() + failureItems.size();
        int successCount = successItems.size();
        int failureCount = failureItems.size();

        String summary = String.format("批量提款完成：总计 %d 笔，成功 %d 笔，失败 %d 笔", 
                                      totalCount, successCount, failureCount);

        return BatchWithdrawalResult.builder()
            .totalCount(totalCount)
            .successCount(successCount)
            .failureCount(failureCount)
            .successItems(successItems)
            .failureItems(failureItems)
            .summary(summary)
            .build();
    }
}
