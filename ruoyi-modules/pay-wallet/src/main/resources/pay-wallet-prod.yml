# pay-wallet 钱包服务配置
spring:
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      seata: false
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.pay-wallet.url}
          username: ${datasource.pay-wallet.username}
          password: ${datasource.pay-wallet.password}

snail-job:
  enabled: true
  # 需要在 SnailJob 后台组管理创建对应名称的组,然后创建任务的时候选择对应的组,才能正确分派任务
  group: "pay_wallet_group"
  #  SnailJob 接入验证令牌
  token: "SJ_Wyz3dmsdbDOkDujOTSSoBjGQP1BMsVnj"
  server:
    # 从 nacos 获取服务
    server-name: ruoyi-snailjob-server
    # 服务名优先 ip垫底
    #host: **************
    port: 17888
  # 详见 sql/ry_job.sql `sj_namespace` 表 `unique_id`
  namespace: ${spring.profiles.active}
  # 随主应用端口飘逸
  port: 2${server.port}
  # 客户端ip指定
  #host: **************
  # RPC类型: netty, grpc
  rpc-type: grpc


# SOLANA 配置
solana:
  # 钱包相关配置
  wallet:
    monitor: false
    compensation: false
    main-address-list:
      - tenant-id: "*********"
        address: "${SOLANA_MAIN_ADDRESS_*********}"  # 生产环境主钱包地址
      - tenant-id: "system2"
        address: "${SOLANA_MAIN_ADDRESS_SYSTEM2}"    # 生产环境系统钱包地址
      - tenant-id: "99"
        address: "${SOLANA_MAIN_ADDRESS_99}"         # 生产环境钱包地址
  # RPC相关配置
  rpc:
    rpcList:
      - "${SOLANA_RPC_ENDPOINT_PRIMARY:https://api.mainnet-beta.solana.com}"
      - "${SOLANA_RPC_ENDPOINT_BACKUP:https://solana-api.projectserum.com}"
    websocketUrl: "${SOLANA_WS_ENDPOINT:wss://api.mainnet-beta.solana.com}"
  # 监控相关配置
  monitor:
    # 监控总开关 - 控制是否启用整个监控功能
    enabled: true
  # 定时补偿配置
  compensation:
    # 总开关 - 控制整个补偿功能的启用状态
    enabled: false
    # 重启时执行开关 - 控制是否在应用重启时立即执行一次补偿
    on-startup: false
  # 合约相关配置 - 主网地址
  contract:
    contracts:
      sol:
        address: "So11111111111111111111111111111111111111112"  # SOL原生代币地址（不变）
        decimals: 9
      usdt:
        address: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"  # USDT主网合约地址
        decimals: 6
      usdc:
        address: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"  # USDC主网合约地址
        decimals: 6

# TRON 配置
tron:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "${TRON_MAIN_ADDRESS_*********}"  # 生产环境主钱包地址
      - tenant-id: "system2"
        address: "${TRON_MAIN_ADDRESS_SYSTEM2}"    # 生产环境系统钱包地址
    fee-wallet:
      enabled: true
      private-key: "${TRON_FEE_WALLET_PRIVATE_KEY}"  # 使用环境变量存储私钥
      address: "${TRON_FEE_WALLET_ADDRESS}"          # 使用环境变量存储地址
      # 统一费用控制配置
      max-fee-limit: 10000000       # 10 TRX (生产环境适当提高限制)
      max-energy-burn: 100000       # 最大能量燃烧限制
      auto-burn-enabled: true       # 自动燃烧TRX支付能量
      auto-burn-bandwidth-enabled: true  # 自动燃烧TRX支付带宽
      # 能量代理API配置
      energy-proxy-enabled: false   # 是否启用能量代理API功能
      energy-proxy-url: ""          # 能量代理API地址，如：http://api.example.com/energy
      energy-proxy-key: "key"       # API密钥参数名
      energy-proxy-hour: 1          # 能量提供小时数
      energy-proxy-value: ""        # API密钥值

  # 统一API配置（合并RPC和HTTP配置）- 主网配置
  api:
    enabled: true
    mode: "http"  # API模式：rpc 或 http
    network-type: "MAINNET"  # 修改为主网
    primary-endpoint: "https://api.trongrid.io"  # 主网API端点
    backup-endpoints:
      - "https://api.tronex.io"      # 主网备用端点
    scan-endpoint: "https://apilist.tronscan.org"  # 主网扫描端点
    api-keys:
      - "${TRON_API_KEY}"  # 使用环境变量存储API密钥
    connection-timeout: 30000  # 连接超时（毫秒）- 建立TCP连接的时间限制
    read-timeout: 60000        # 读取超时（毫秒）- 等待服务器响应的时间限制
    user-agent: "TronWallet/1.0"  # 用户代理 - 帮助API识别客户端类型

  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: true          # 是否启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 15000          # 自动扫描周期（毫秒），TRON出块时间约3秒，建议15秒

  contract:
    enabled: true
    contracts:
      usdt:
        address: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"  # USDT主网合约地址
        decimals: 6
        description: "USDT主网代币"
        enabled: true
        min-amount: 1.0
        max-amount: 100000.0  # 生产环境提高限额
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8"  # USDC主网合约地址（需要确认）
        decimals: 6
        description: "USDC主网代币"
        enabled: true
        min-amount: 1.0
        max-amount: 50000.0   # 生产环境提高限额
        symbol: "USDC"
        name: "USD Coin"

# BSC 配置
bsc:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "${BSC_MAIN_ADDRESS_*********}"  # 生产环境主钱包地址
    fee-wallet:
      enabled: true
      private-key: "${BSC_FEE_WALLET_PRIVATE_KEY}"  # 使用环境变量存储私钥
      address: "${BSC_FEE_WALLET_ADDRESS}"          # 使用环境变量存储地址
      # 统一费用控制配置
      max-gas-price: 20000000000  # 20 gwei (BSC主网安全上限)
      max-gas-limit: 8000000      # 8M gas (区块gas限制)

  rpc:
    enabled: true
    network-type: "MAINNET"  # 修改为主网
    chain-id: 56  # BSC主网Chain ID
    endpoint: "${BSC_RPC_ENDPOINT:https://bsc-dataseed1.binance.org/}"  # BSC主网RPC端点
    connection-timeout: 30
    read-timeout: 60

  contract:
    enabled: true
    contracts:
      usdt:
        address: "******************************************"  # USDT主网合约地址
        decimals: 18
        description: "USDT主网代币"
        enabled: true
        min-amount: 0.01
        max-amount: 100000.0  # 生产环境提高限额
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"  # USDC主网合约地址
        decimals: 18
        description: "USDC主网代币"
        enabled: true
        min-amount: 0.01
        max-amount: 50000.0   # 生产环境提高限额
        symbol: "USDC"
        name: "USD Coin"


  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: true          # 是否启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 15000          # 自动扫描周期（毫秒），BSC出块时间约3秒，建议15秒

# ARB (Arbitrum) 配置
arb:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "${ARB_MAIN_ADDRESS_*********}"  # 生产环境主钱包地址
    fee-wallet:
      enabled: true
      private-key: "${ARB_FEE_WALLET_PRIVATE_KEY}"  # 使用环境变量存储私钥
      address: "${ARB_FEE_WALLET_ADDRESS}"          # 使用环境变量存储地址
      # 统一费用控制配置
      max-gas-price: 1000000000   # 1 gwei (Layer 2更便宜)
      max-gas-limit: 8000000      # 8M gas (区块gas限制)

  rpc:
    enabled: true
    network-type: "MAINNET"  # 修改为主网
    chain-id: 42161  # Arbitrum One主网Chain ID
    endpoint: "${ARB_RPC_ENDPOINT:https://arb1.arbitrum.io/rpc}"  # Arbitrum主网RPC端点
    connection-timeout: 30
    read-timeout: 60

  contract:
    enabled: true
    contracts:
      usdt:
        address: "******************************************"  # USDT主网合约地址
        decimals: 6
        description: "USDT主网代币"
        enabled: true
        min-amount: 0.01
        max-amount: 100000.0  # 生产环境提高限额
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"  # USDC主网合约地址
        decimals: 6
        description: "USDC主网代币"
        enabled: true
        min-amount: 0.01
        max-amount: 50000.0   # 生产环境提高限额
        symbol: "USDC"
        name: "USD Coin"


  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: true          # 是否启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 15000          # 自动扫描周期（毫秒），ARB出块时间约0.25秒，建议15秒

# BASE 配置
base:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "${BASE_MAIN_ADDRESS_*********}"  # 生产环境主钱包地址
    fee-wallet:
      enabled: true
      private-key: "${BASE_FEE_WALLET_PRIVATE_KEY}"  # 使用环境变量存储私钥
      address: "${BASE_FEE_WALLET_ADDRESS}"          # 使用环境变量存储地址
      # 统一费用控制配置
      max-gas-price: 1000000000   # 1 gwei (Layer 2更便宜)
      max-gas-limit: 8000000      # 8M gas (区块gas限制)

  rpc:
    enabled: true
    network-type: "MAINNET"  # 修改为主网
    chain-id: 8453  # Base主网Chain ID
    endpoint: "${BASE_RPC_ENDPOINT:https://mainnet.base.org}"  # Base主网RPC端点
    connection-timeout: 30
    read-timeout: 60

  contract:
    enabled: true
    contracts:
      usdt:
        address: "******************************************"  # Base主网暂无USDT，使用占位符
        decimals: 6
        description: "USDT主网代币（暂不支持）"
        enabled: false  # 暂时禁用
        min-amount: 0.01
        max-amount: 100000.0
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"  # USDC主网合约地址
        decimals: 6
        description: "USDC主网代币"
        enabled: true
        min-amount: 0.01
        max-amount: 50000.0   # 生产环境提高限额
        symbol: "USDC"
        name: "USD Coin"


  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: true          # 是否启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 15000          # 自动扫描周期（毫秒），BASE出块时间约2秒，建议15秒

# AVAX (Avalanche) 配置
avax:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "${AVAX_MAIN_ADDRESS_*********}"  # 生产环境主钱包地址
    fee-wallet:
      enabled: true
      private-key: "${AVAX_FEE_WALLET_PRIVATE_KEY}"  # 使用环境变量存储私钥
      address: "${AVAX_FEE_WALLET_ADDRESS}"          # 使用环境变量存储地址
      # 统一费用控制配置
      max-gas-price: 30000000000  # 30 gwei (AVAX网络通常比BSC稍高)
      max-gas-limit: 8000000      # 8M gas (区块gas限制)

  rpc:
    enabled: true
    network-type: "MAINNET"  # 修改为主网
    chain-id: 43114  # AVAX C-Chain主网Chain ID
    endpoint: "${AVAX_RPC_ENDPOINT:https://api.avax.network/ext/bc/C/rpc}"  # AVAX主网RPC端点
    connection-timeout: 30
    read-timeout: 60

  contract:
    enabled: true
    contracts:
      usdt:
        address: "******************************************"  # USDT主网合约地址
        decimals: 6
        description: "USDT主网代币"
        enabled: true
        min-amount: 0.01
        max-amount: 100000.0  # 生产环境提高限额
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"  # USDC主网合约地址
        decimals: 6
        description: "USDC主网代币"
        enabled: true
        min-amount: 0.01
        max-amount: 50000.0   # 生产环境提高限额
        symbol: "USDC"
        name: "USD Coin"


  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: true          # 生产环境启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 8000           # 自动扫描周期（毫秒），AVAX出块时间约2秒，建议6秒


# 私钥加密配置
wallet-encryption:
  # 是否启用加密功能
  enabled: true
  # 加密密钥 (生产环境必须使用环境变量)
  secret-key: "${WALLET_ENCRYPTION_SECRET_KEY}"  # 必须通过环境变量配置
  # 加密算法 支持: AES, DES, 3DES 等
  algorithm: "AES"
  # 编码方式 支持: BASE64, HEX
  encoding: "BASE64"
