package org.dromara.wallet.wallet.helper;

import cn.hutool.core.thread.ThreadUtil;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.ChainConfigFacadeManager;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.dromara.wallet.wallet.exception.BlockchainTransferException;
import org.dromara.wallet.wallet.exception.WalletException;
import org.dromara.wallet.wallet.transfer.config.TransactionConfirmationConfig;
import org.dromara.wallet.wallet.transfer.dto.TransactionConfirmationResult;
import org.dromara.wallet.wallet.transfer.service.TransactionConfirmationService;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.protocol.http.HttpService;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * EVM链操作助手类 - 扁平化配置版本
 * 支持BSC、ARB、BASE等EVM兼容链
 *
 * <p>主要功能包括：</p>
 * <ul>
 *   <li>配置管理和验证</li>
 *   <li>余额查询（原生代币和合约代币）</li>
 *   <li>转账操作（支持手续费钱包）</li>
 *   <li>Gas费用估算和管理</li>
 *   <li>多链支持（BSC、ARB、BASE）</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EvmHelper implements TransactionConfirmationService {

    private final ChainConfigFacadeManager chainConfigFacadeManager;

    // ==================== Web3j连接缓存 ====================

    /**
     * Web3j连接缓存，使用endpoint作为key实现连接复用
     * 解决每次调用configGetWeb3j都创建新连接的问题
     */
    private final ConcurrentHashMap<String, Web3j> web3jCache = new ConcurrentHashMap<>();

    // ==================== 常量定义 ====================

    /**
     * ERC20 Transfer事件签名: Transfer(address indexed from, address indexed to, uint256 value)
     * Keccak256哈希值: 0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef
     */
    private static final String TRANSFER_EVENT_TOPIC = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";

    // ==================== 配置管理和验证 ====================

    /**
     * 验证EVM配置是否可用
     *
     * @param facade EVM配置门面
     * @throws WalletException 当配置无效时抛出异常
     */
    private void configValidateEvmConfig(EvmConfigFacade facade) {
        if (facade == null) {
            throw new WalletException("EVM配置门面不能为空");
        }
        if (!facade.isEnabled()) {
            throw new WalletException(facade.getChainName() + "链未启用");
        }
        if (facade.getPrimaryEndpoint() == null || facade.getPrimaryEndpoint().trim().isEmpty()) {
            throw new WalletException(facade.getChainName() + "链端点未配置");
        }
    }

    /**
     * 获取Web3j实例 - 优化版本，支持连接复用
     * 根据不同的EVM链配置创建对应的Web3j连接，使用缓存避免重复创建
     *
     * @param facade EVM配置门面
     * @return Web3j实例
     * @throws WalletException 当配置无效时抛出异常
     */
    public Web3j configGetWeb3j(EvmConfigFacade facade) {
        configValidateEvmConfig(facade);
        String endpoint = facade.getPrimaryEndpoint();

        // 使用缓存获取或创建Web3j连接，避免重复创建
        return web3jCache.computeIfAbsent(endpoint, this::createWeb3jInstance);
    }

    /**
     * 创建Web3j实例的内部方法
     *
     * @param endpoint RPC端点地址
     * @return Web3j实例
     */
    private Web3j createWeb3jInstance(String endpoint) {
        // 创建HTTP服务，支持超时配置
        HttpService httpService = new HttpService(endpoint);

        log.debug("创建Web3j连接实例: endpoint={}", endpoint);
        return org.web3j.protocol.Web3j.build(httpService);
    }

    /**
     * 清理Web3j连接缓存
     * 在Bean销毁时调用，确保资源正确释放
     */
    @PreDestroy
    public void cleanup() {
        log.info("开始清理Web3j连接缓存，当前缓存数量: {}", web3jCache.size());
        web3jCache.values().forEach(web3j -> {
            try {
                web3j.shutdown();
            } catch (Exception e) {
                log.warn("关闭Web3j连接时出错: {}", e.getMessage());
            }
        });
        web3jCache.clear();
        log.info("Web3j连接缓存清理完成");
    }

    /**
     * 根据链名获取原生代币符号
     *
     * @param facade EVM配置门面
     * @return 原生代币符号
     */
    public String configGetNativeTokenSymbol(EvmConfigFacade facade) {
        String chainName = facade.getChainName().toUpperCase();
        return switch (chainName) {
            case "BSC" -> "BNB";
            case "ARB", "ARBITRUM" -> "ETH";
            case "BASE" -> "ETH";
            default -> "ETH"; // 默认为ETH
        };
    }

    // ==================== 余额查询方法 ====================

    /**
     * 获取原生代币余额（原始格式）- 内部方法
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     *
     * @param address 钱包地址
     * @param facade  EVM配置门面
     * @return 原生代币余额（Wei单位）
     * @throws WalletException 当查询失败时抛出异常
     */
    @Retryable(retryFor = {IOException.class, WalletException.class},
        backoff = @Backoff(delay = 500, multiplier = 2.0))
    private BigInteger balanceGetNativeRaw(String address, EvmConfigFacade facade) {
        Web3j web3j = configGetWeb3j(facade);

        try {
            EthGetBalance ethGetBalance = web3j.ethGetBalance(address, DefaultBlockParameterName.LATEST).send();
            BigInteger balance = ethGetBalance.getBalance();
            log.debug("{}链原生代币余额查询成功: address={}, balance={} Wei",
                facade.getChainName(), address, balance);
            return balance;
        } catch (IOException e) {
            log.error("{}链原生代币余额查询失败: address={}, error={}",
                facade.getChainName(), address, e.getMessage());
            throw new WalletException(facade.getChainName() + "获取原生代币余额失败: " + e.getMessage());
        }
    }

    /**
     * 获取原生代币余额（可读格式）
     * 基础API方法，提供给Strategy层调用
     *
     * @param address 钱包地址
     * @param facade  EVM配置门面
     * @return 原生代币余额（以Ether为单位，保留6位小数）
     */
    public BigDecimal balanceGetNativeForRead(String address, EvmConfigFacade facade) {
        BigInteger balance = balanceGetNativeRaw(address, facade);
        // 成功和异常日志都由统一入口方法记录，避免重复
        return new BigDecimal(balance).divide(new BigDecimal(10).pow(18), 6, RoundingMode.DOWN);
    }

    /**
     * 获取合约代币余额（原始格式）- 内部方法
     * 支持ERC20(ETH/ARB/BASE)、BEP20(BSC)等标准
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     *
     * @param address         用户地址
     * @param contractAddress 合约地址
     * @param facade          EVM配置门面
     * @return 代币余额（原始单位）
     * @throws WalletException 当查询失败时抛出异常
     */
    @Retryable(retryFor = {IOException.class, WalletException.class},
        backoff = @Backoff(delay = 500, multiplier = 2.0))
    private BigInteger balanceGetTokenRaw(String address, String contractAddress, EvmConfigFacade facade) {
        Web3j web3j = configGetWeb3j(facade);

        // 创建balanceOf函数调用
        Function function = new Function("balanceOf",
            List.of(new Address(address)),
            Collections.singletonList(new TypeReference<Uint256>() {
            }));
        // 创建调用交易
        Transaction transaction = Transaction.createEthCallTransaction(address, contractAddress, FunctionEncoder.encode(function));
        // 发送交易并获取返回结果
        try {
            EthCall response = web3j.ethCall(transaction, DefaultBlockParameterName.LATEST).send();
            // 解析返回结果
            String value = response.getValue();
            // 添加空值检查和处理
            if (value == null || value.isEmpty() || "0x".equals(value)) {
                log.debug("{}链合约代币余额为0: address={}, contract={}",
                    facade.getChainName(), address, contractAddress);
                return BigInteger.ZERO;
            }
            BigInteger balance = Numeric.toBigInt(value);
            log.debug("{}链合约代币余额查询成功: address={}, contract={}, balance={}",
                facade.getChainName(), address, contractAddress, balance);
            return balance;
        } catch (IOException e) {
            log.error("{}链合约代币余额查询失败: address={}, contract={}, error={}",
                facade.getChainName(), address, contractAddress, e.getMessage());
            throw new WalletException(facade.getChainName() + "获取合约代币余额失败: " + e.getMessage());
        }
    }

    /**
     * 获取合约代币余额（可读格式）- 内部方法
     * 支持ERC20(ETH/ARB/BASE)、BEP20(BSC)等标准
     *
     * @param address     钱包地址
     * @param tokenSymbol 代币符号（用于获取合约地址和精度）
     * @param facade      EVM配置门面
     * @return 可读格式的余额（保留6位小数）
     * @throws WalletException 当不支持指定代币时抛出异常
     */
    private BigDecimal balanceGetTokenForRead(String address, String tokenSymbol, EvmConfigFacade facade) {
        // 通过代币符号获取合约地址
        String contractAddress = facade.getContractAddress(tokenSymbol);
        if (contractAddress == null || contractAddress.trim().isEmpty()) {
            throw new WalletException(facade.getChainName() + "链不支持代币: " + tokenSymbol);
        }

        BigInteger rawBalance = balanceGetTokenRaw(address, contractAddress, facade);
        if (rawBalance == null || rawBalance.equals(BigInteger.ZERO)) {
            return BigDecimal.ZERO;
        }

        // 获取代币精度
        int decimals = facade.getContractDecimals(tokenSymbol);
        BigDecimal divisor = new BigDecimal(10).pow(decimals);

        // 成功和异常日志都由统一入口方法记录，避免重复
        return new BigDecimal(rawBalance).divide(divisor, 6, RoundingMode.DOWN);
    }

    /**
     * 统一的代币余额查询方法（可读格式）- 唯一公共入口
     * 自动区分原生代币和合约代币，这是余额查询的统一入口方法
     * 所有余额查询都应该通过此方法进行，其他方法为内部实现
     *
     * @param address     钱包地址
     * @param tokenSymbol 代币符号
     * @param facade      EVM配置门面
     * @return 可读格式的余额（保留6位小数）
     * @throws WalletException 当不支持指定代币时抛出异常
     */
    public BigDecimal balanceGetForRead(String address, String tokenSymbol, EvmConfigFacade facade) {
        try {
            // 判断原生代币
            String nativeSymbol = configGetNativeTokenSymbol(facade);
            BigDecimal balance;

            if (nativeSymbol.equalsIgnoreCase(tokenSymbol)) {
                // 原生代币 - 直接调用原生代币查询方法
                balance = balanceGetNativeForRead(address, facade);
            } else {
                // 合约代币 - 调用合约代币查询方法
                balance = balanceGetTokenForRead(address, tokenSymbol, facade);
            }

            // 统一的成功日志（避免重复记录）
            if (balance.compareTo(BigDecimal.ZERO) > 0) {
                log.info("{}链余额查询: address={}, token={}, balance={}",
                    facade.getChainName(), address, tokenSymbol, balance);
            }

            return balance;
        } catch (Exception e) {
            log.error("{}链余额查询失败: address={}, token={}, error={}",
                facade.getChainName(), address, tokenSymbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    // ==================== 转账操作方法 ====================

    /**
     * 原生代币转账 - 基础API方法
     * 提供给Strategy层调用的基础转账功能
     *
     * @param fromAddress 发送方地址
     * @param privateKey  发送方私钥
     * @param amount      转账金额（可读格式，以Ether为单位）
     * @param toAddress   接收方地址
     * @param facade      EVM配置门面
     * @return 交易哈希
     * @throws WalletException 当转账失败时抛出异常
     */
    public String transferNative(String fromAddress, String privateKey, BigDecimal amount,
                                 String toAddress, EvmConfigFacade facade) {
        Web3j web3j = configGetWeb3j(facade);

        try {
            // 创建凭证
            Credentials credentials = Credentials.create(privateKey);

            // 转换金额为Wei（18位小数）
            BigInteger amountWei = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger();

            // 获取nonce
            EthGetTransactionCount ethGetTransactionCount = web3j
                .ethGetTransactionCount(fromAddress, DefaultBlockParameterName.LATEST).send();
            BigInteger nonce = ethGetTransactionCount.getTransactionCount();

            // 获取gas价格
            EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
            BigInteger gasPrice = ethGasPrice.getGasPrice();

            // 设置gas限制（原生代币转账通常21000）
            BigInteger gasLimit = BigInteger.valueOf(21000);

            // 创建原始交易
            RawTransaction rawTransaction = RawTransaction.createEtherTransaction(
                nonce, gasPrice, gasLimit, toAddress, amountWei);

            // 签名交易
            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction,
                facade.getChainId(), credentials);
            String hexValue = Numeric.toHexString(signedMessage);

            // 发送交易
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).send();

            if (ethSendTransaction.hasError()) {
                throw new WalletException(facade.getChainName() + "原生代币转账失败: " +
                    ethSendTransaction.getError().getMessage());
            }

            String txHash = ethSendTransaction.getTransactionHash();
            log.info("{}原生代币转账成功: from={}, to={}, amount={}, txHash={}",
                facade.getChainName(), fromAddress, toAddress, amount, txHash);

            return txHash;

        } catch (Exception e) {
            log.error("{}原生代币转账失败", facade.getChainName(), e);
            throw new WalletException(facade.getChainName() + "原生代币转账失败: " + e.getMessage());
        }
    }

    // ==================== 手续费处理方法 ====================

    /**
     * 估算合约代币转账的gas限制
     * 支持ERC20(ETH/ARB/BASE)、BEP20(BSC)等标准
     * <p>
     * 改进说明：
     * - 为BSC等严格的节点提供完整的交易参数（nonce、gasPrice、gasLimit）
     * - 提高gas估算的成功率和准确性
     * - 增强错误处理和日志记录
     *
     * @param web3j           Web3j实例
     * @param fromAddress     发送方地址
     * @param contractAddress 合约地址
     * @param encodedFunction 编码后的函数调用
     * @param facade          EVM配置门面
     * @return 估算的gas限制（包含20%缓冲）
     */
    public BigInteger transferEstimateGasForToken(Web3j web3j, String fromAddress,
                                                  String contractAddress, String encodedFunction,
                                                  EvmConfigFacade facade) {
        try {
            // 获取必要的交易参数以提高估算成功率
            BigInteger nonce = null;
            BigInteger gasPrice = null;
            BigInteger gasLimit = null;

            try {
                // 获取nonce（某些节点需要）
                EthGetTransactionCount ethGetTransactionCount = web3j
                    .ethGetTransactionCount(fromAddress, DefaultBlockParameterName.LATEST).send();
                nonce = ethGetTransactionCount.getTransactionCount();

                // 获取当前gas价格（BSC节点可能需要）
                EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
                gasPrice = ethGasPrice.getGasPrice();

                // 应用gas价格上限（防止网络拥堵时价格过高）
                BigInteger maxGasPrice = BigInteger.valueOf(facade.getMaxGasPrice());
                if (gasPrice.compareTo(maxGasPrice) > 0) {
                    gasPrice = maxGasPrice;
                    log.debug("{}链gas价格超过上限，使用配置上限: {} wei", facade.getChainName(), maxGasPrice);
                }

                // 设置合理的gas限制上限（用于估算边界）
                gasLimit = BigInteger.valueOf(facade.getMaxGasLimit());
            } catch (Exception paramException) {
                log.debug("{}链获取交易参数失败，使用null参数进行估算: {}",
                    facade.getChainName(), paramException.getMessage());
                // 如果获取参数失败，继续使用null参数尝试估算
            }

            // 创建估算gas的交易（提供完整参数以提高BSC等链的兼容性）
            Transaction transaction = Transaction.createFunctionCallTransaction(
                fromAddress, nonce, gasPrice, gasLimit, contractAddress, encodedFunction);

            log.debug("{}链开始gas估算: from={}, contract={}, nonce={}, gasPrice={}, gasLimit={}",
                facade.getChainName(), fromAddress, contractAddress, nonce, gasPrice, gasLimit);

            EthEstimateGas ethEstimateGas = web3j.ethEstimateGas(transaction).send();

            if (ethEstimateGas.hasError()) {
                String errorMessage = ethEstimateGas.getError().getMessage();
                String errorCode = ethEstimateGas.getError().getCode() != 0 ?
                    String.valueOf(ethEstimateGas.getError().getCode()) : "unknown";

                log.warn("{}链gas估算失败: code={}, message={}", facade.getChainName(), errorCode, errorMessage);

                // 检查是否为ERC20InsufficientBalance错误 (0xe450d38c) - 优先处理
                if (errorMessage != null && errorMessage.toLowerCase().contains("0xe450d38c")) {
                    String detailedError = parseErc20InsufficientBalanceError(errorMessage);
                    log.info("{}链gas估算检测到余额不足: {}", facade.getChainName(), detailedError);

                    // 余额不足时使用合理的代币转账gas限制，而不是最大值
                    BigInteger reasonableGasLimit = BigInteger.valueOf(100_000L); // 代币转账的合理gas限制
                    log.info("{}链使用合理的代币转账gas限制: {}", facade.getChainName(), reasonableGasLimit);
                    return reasonableGasLimit;
                }

                // 检查其他类型的余额不足或明显会导致交易失败的错误
                if (errorMessage != null) {
                    String lowerMessage = errorMessage.toLowerCase();
                    if (lowerMessage.contains("transfer amount exceeds balance") ||
                        lowerMessage.contains("insufficient balance") ||
                        lowerMessage.contains("exceeds balance") ||
                        lowerMessage.contains("bep40: transfer amount exceeds balance") ||
                        lowerMessage.contains("erc20: transfer amount exceeds balance")) {
                        throw new WalletException("余额不足，无法执行交易: " + errorMessage);
                    }

                    // 排除已处理的余额不足错误
                    if (lowerMessage.contains("execution reverted") ||
                        lowerMessage.contains("invalid opcode") ||
                        lowerMessage.contains("out of gas")) {
                        // 如果是余额不足错误，已经在前面处理过了，这里不再重复处理
                        if (!lowerMessage.contains("0xe450d38c")) {
                            log.warn("{}链检测到合约执行问题: {}", facade.getChainName(), errorMessage);
                        }
                        // 错误检测到后，返回合理的代币转账gas限制，避免继续执行后续逻辑
                        BigInteger reasonableGasLimit = BigInteger.valueOf(100_000L); // 代币转账的合理gas限制
                        log.info("{}链使用合理的代币转账gas限制: {}", facade.getChainName(), reasonableGasLimit);
                        return reasonableGasLimit;
                    }
                }

                // 根据链类型返回合理的代币转账gas限制
                BigInteger reasonableGasLimit = BigInteger.valueOf(100_000L); // 代币转账的合理gas限制
                log.info("{}链使用合理的代币转账gas限制: {}", facade.getChainName(), reasonableGasLimit);
                return reasonableGasLimit;
            }

            BigInteger estimatedGas = ethEstimateGas.getAmountUsed();
            // 增加20%的缓冲
            BigInteger finalGasLimit = estimatedGas.multiply(BigInteger.valueOf(120)).divide(BigInteger.valueOf(100));

            log.debug("{}链gas估算成功: estimated={}, final={}",
                facade.getChainName(), estimatedGas, finalGasLimit);

            return finalGasLimit;

        } catch (Exception e) {
            // 检查是否为ERC20InsufficientBalance错误 (0xe450d38c)
            if (isInsufficientBalanceError(e)) {
                String errorMessage = e.getMessage();
                if (errorMessage != null && errorMessage.toLowerCase().contains("0xe450d38c")) {
                    String detailedError = parseErc20InsufficientBalanceError(errorMessage);
                    log.info("{}链gas估算异常检测到余额不足: {}", facade.getChainName(), detailedError);

                    // 余额不足时使用合理的代币转账gas限制，不抛出异常
                    BigInteger reasonableGasLimit = BigInteger.valueOf(100_000L); // 代币转账的合理gas限制
                    log.info("{}链使用合理的代币转账gas限制: {}", facade.getChainName(), reasonableGasLimit);
                    return reasonableGasLimit;
                } else {
                    // 其他类型的余额不足错误，抛出异常
                    throw new WalletException("余额不足，无法执行交易: " + errorMessage);
                }
            }

            // 记录其他类型的异常
            log.warn("{}链gas估算异常: {}", facade.getChainName(), e.getMessage());

            // 根据链类型返回合理的代币转账gas限制
            BigInteger reasonableGasLimit = BigInteger.valueOf(100_000L); // 代币转账的合理gas限制
            log.info("{}链估算异常，使用合理的代币转账gas限制: {}", facade.getChainName(), reasonableGasLimit);
            return reasonableGasLimit;
        }
    }

    // ==================== 统一异常转换器 ====================

    /**
     * 统一的EVM异常处理器
     *
     * <p>将所有EVM相关的异常统一转换为BlockchainTransferException，</p>
     * <p>避免在业务逻辑中出现大量的try-catch块。</p>
     *
     * @param operation 要执行的操作
     * @param <T>       返回类型
     * @return 操作结果
     * @throws BlockchainTransferException 转换后的统一异常
     */
    public <T> T executeWithEvmExceptionHandling(java.util.function.Supplier<T> operation) throws BlockchainTransferException {
        try {
            return operation.get();
        } catch (BlockchainTransferException e) {
            // 已经是我们的异常，直接重新抛出
            throw e;
        } catch (WalletException e) {
            // EVM特定异常，转换为统一异常
            throw BlockchainTransferException.wrap("EVM", "TRANSFER_FAILED", e);
        } catch (Exception e) {
            // 其他异常，根据类型进行智能转换
            return handleGenericEvmException(e);
        }
    }

    /**
     * 智能处理EVM通用异常
     */
    private <T> T handleGenericEvmException(Exception e) throws BlockchainTransferException {
        String message = e.getMessage();
        String exceptionType = e.getClass().getSimpleName();

        // 余额不足
        if (isInsufficientBalanceError(e)) {
            throw BlockchainTransferException.insufficientBalance("EVM", BigDecimal.ZERO);
        }

        // 网络相关错误
        if (isNetworkError(e)) {
            throw BlockchainTransferException.networkTimeout("EVM", e);
        }

        // Gas费相关错误
        if (isGasRelatedError(e)) {
            throw new BlockchainTransferException(
                "Gas费用不足或设置错误: " + message,
                "EVM",
                "INSUFFICIENT_GAS",
                e
            );
        }

        // JSON解析错误
        if (exceptionType.contains("Json") || exceptionType.contains("Parse")) {
            throw new BlockchainTransferException(
                "EVM API响应解析失败: " + message,
                "EVM",
                "API_PARSE_ERROR",
                e
            );
        }

        // 配置错误
        if (message != null && (message.contains("config") || message.contains("配置"))) {
            throw BlockchainTransferException.configurationError("EVM", "unknown");
        }

        // 默认服务错误
        throw BlockchainTransferException.wrap("EVM", "SERVICE_ERROR", e);
    }

    // ==================== 地址推导工具方法 ====================

    /**
     * 从私钥推导EVM地址
     *
     * @param privateKey 私钥（十六进制字符串，可选0x前缀）
     * @return EVM地址（小写，带0x前缀）
     * @throws IllegalArgumentException 当私钥格式无效时抛出
     */
    public String getAddressFromPrivateKey(String privateKey) {
        try {
            Credentials credentials = Credentials.create(privateKey);
            return credentials.getAddress().toLowerCase();
        } catch (Exception e) {
            log.error("从私钥推导EVM地址失败: {}", e.getMessage());
            throw new IllegalArgumentException("无效的私钥格式: " + e.getMessage(), e);
        }
    }

    /**
     * 安全地将十六进制字符串转换为BigInteger
     *
     * @param hexString 十六进制字符串（可能为null或空）
     * @return BigInteger值，如果输入无效则返回null
     */
    private BigInteger parseHexToBigInteger(String hexString) {
        if (hexString == null || hexString.trim().isEmpty()) {
            return null;
        }
        try {
            // 移除0x前缀
            String cleanHex = hexString.startsWith("0x") ? hexString.substring(2) : hexString;
            return new BigInteger(cleanHex, 16);
        } catch (NumberFormatException e) {
            log.warn("无法解析十六进制字符串: {}", hexString);
            return null;
        }
    }
    // ==================== 交易验证方法 ====================

    /**
     * 执行轻量级确认的核心逻辑 - 私有方法
     * 提取公共逻辑，避免代码重复和循环引用
     *
     * @param transactionHash       交易哈希
     * @param facade                EVM配置门面
     * @param timeoutSeconds        超时时间（秒）
     * @param checkInterval         检查间隔（秒）
     * @param requiredConfirmations 所需确认数
     * @return 验证结果
     */
    private EvmTransferResult performLightConfirmation(String transactionHash, EvmConfigFacade facade,
                                                       int timeoutSeconds, int checkInterval, int requiredConfirmations) {
        try {
            Web3j web3j = configGetWeb3j(facade);
            long startTime = System.currentTimeMillis();

            log.info("开始EVM交易确认: {}, 超时: {}秒, 所需确认数: {}",
                transactionHash, timeoutSeconds, requiredConfirmations);

            while (System.currentTimeMillis() - startTime < timeoutSeconds * 1000L) {
                try {
                    // 获取交易回执
                    Optional<TransactionReceipt> receiptOpt = web3j.ethGetTransactionReceipt(transactionHash)
                        .send().getTransactionReceipt();

                    if (receiptOpt.isPresent()) {
                        TransactionReceipt receipt = receiptOpt.get();

                        // 检查交易执行状态
                        if ("0x0".equals(receipt.getStatus())) {
                            log.warn("交易执行失败: {}, 状态码: 0x0", transactionHash);
                            return EvmTransferResult.failure("交易执行失败，状态码: 0x0");
                        }

                        // 计算确认数
                        BigInteger currentBlockNumber = web3j.ethBlockNumber().send().getBlockNumber();
                        BigInteger transactionBlockNumber = receipt.getBlockNumber();
                        int confirmations = currentBlockNumber.subtract(transactionBlockNumber).intValue() + 1;

                        log.debug("交易 {} 当前确认数: {}/{}", transactionHash, confirmations, requiredConfirmations);

                        if (confirmations >= requiredConfirmations) {
                            log.info("EVM交易确认成功: {}, 确认数: {}", transactionHash, confirmations);
                            return EvmTransferResult.success(transactionHash, false, BigDecimal.ZERO,
                                TransactionStatus.SUCCESS, transactionBlockNumber.longValue(), confirmations,
                                receipt.getGasUsed(),
                                parseHexToBigInteger(receipt.getEffectiveGasPrice()));
                        }
                    }

                    ThreadUtil.sleep(checkInterval * 1000L);

                } catch (Exception e) {
                    log.debug("检查交易状态异常，继续重试: {}", e.getMessage());
                    ThreadUtil.sleep(checkInterval * 1000L);
                }
            }

            log.warn("EVM交易确认超时: {}", transactionHash);
            return EvmTransferResult.timeout(transactionHash, "交易确认超时");

        } catch (Exception e) {
            log.error("EVM交易确认异常: {}", transactionHash, e);
            return EvmTransferResult.failure("交易确认异常: " + e.getMessage());
        }
    }

    /**
     * 快速状态检查
     * 只检查交易是否上链且成功，不等待确认，适用于手续费验证
     * 提供给Strategy层调用的基础API
     *
     * @param transactionHash 交易哈希
     * @param facade          EVM配置门面
     * @return 是否成功
     */
    public boolean quickStatusCheck(String transactionHash, EvmConfigFacade facade) {
        try {
            Web3j web3j = configGetWeb3j(facade);
            long startTime = System.currentTimeMillis();

            while (System.currentTimeMillis() - startTime < 15 * 1000L) {
                try {
                    Optional<TransactionReceipt> receiptOpt = web3j.ethGetTransactionReceipt(transactionHash)
                        .send().getTransactionReceipt();

                    if (receiptOpt.isPresent()) {
                        TransactionReceipt receipt = receiptOpt.get();
                        boolean success = "0x1".equals(receipt.getStatus());
                        log.debug("交易 {} 状态检查结果: {}", transactionHash, success ? "成功" : "失败");
                        return success;
                    }

                    ThreadUtil.sleep(2000); // 2秒后重试

                } catch (Exception e) {
                    log.debug("状态检查异常，继续重试: {}", e.getMessage());
                    ThreadUtil.sleep(2000);
                }
            }

            log.warn("交易状态检查超时: {}", transactionHash);
            return false;

        } catch (Exception e) {
            log.error("状态检查异常: {}", transactionHash, e);
            return false;
        }
    }

    // ==================== 监听和过滤器方法 ====================

    /**
     * 直接获取事件日志记录 - 推荐使用
     * 使用eth_getLogs API直接获取日志，替代ethNewFilter+getTransactions的两步操作
     *
     * <p>功能特性：</p>
     * <ul>
     *   <li>一步操作直接获取日志，性能更优</li>
     *   <li>支持指定区块范围（fromBlock, toBlock）</li>
     *   <li>支持多个合约地址过滤</li>
     *   <li>监听Transfer事件（使用预定义的TRANSFER_EVENT_TOPIC）</li>
     *   <li>集成重试机制和统一错误处理</li>
     *   <li>无需在节点上创建过滤器对象，节约资源</li>
     * </ul>
     *
     * @param fromBlock 开始区块
     * @param toBlock   结束区块
     * @param tokenList 合约地址列表
     * @param facade    EVM配置门面
     * @return 日志记录列表，如果没有找到记录则返回空列表
     * @throws WalletException 当获取日志失败时抛出异常
     */
    @Retryable(retryFor = {IOException.class, WalletException.class},
        backoff = @Backoff(delay = 500, multiplier = 2.0))
    public List<org.web3j.protocol.core.methods.response.Log> ethGetLogs(BigInteger fromBlock, BigInteger toBlock,
                                                                         List<String> tokenList, EvmConfigFacade facade) {
        // 参数验证
        if (fromBlock == null) {
            throw new WalletException("开始区块不能为空");
        }
        if (toBlock == null) {
            throw new WalletException("结束区块不能为空");
        }
        if (tokenList == null || tokenList.isEmpty()) {
            throw new WalletException("代币合约地址列表不能为空");
        }

        Web3j web3j = configGetWeb3j(facade);

        try {
            log.debug("{}链直接获取日志: fromBlock={}, toBlock={}, contracts={}",
                facade.getChainName(), fromBlock, toBlock, tokenList.size());

            // 创建过滤器条件（不在节点上创建过滤器对象）
            EthFilter ethFilter = new EthFilter(
                DefaultBlockParameter.valueOf(fromBlock),
                DefaultBlockParameter.valueOf(toBlock),
                tokenList
            );

            // 添加Transfer事件topic
            ethFilter.addSingleTopic(TRANSFER_EVENT_TOPIC);

            // 直接获取日志记录
            EthLog ethLog = web3j.ethGetLogs(ethFilter).send();

            // 处理响应
            List<org.web3j.protocol.core.methods.response.Log> logs = new ArrayList<>();
            if (ethLog.getLogs() != null) {
                for (EthLog.LogResult<?> logResult : ethLog.getLogs()) {
                    if (logResult instanceof EthLog.LogObject) {
                        logs.add(((EthLog.LogObject) logResult).get());
                    }
                }
            }

            log.info("{}链成功获取日志: fromBlock={}, toBlock={}, contracts={}, 日志数量={}",
                facade.getChainName(), fromBlock, toBlock, tokenList.size(), logs.size());

            return logs;

        } catch (IOException e) {
            log.error("{}链获取日志失败: fromBlock={}, toBlock={}, error={}",
                facade.getChainName(), fromBlock, toBlock, e.getMessage());
            throw new WalletException(facade.getChainName() + "获取日志失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("{}链获取日志异常: fromBlock={}, toBlock={}, error={}",
                facade.getChainName(), fromBlock, toBlock, e.getMessage());
            throw new WalletException(facade.getChainName() + "获取日志异常: " + e.getMessage());
        }
    }

    // ==================== 区块查询方法 ====================

    /**
     * 获取最新区块高度
     * 查询EVM链的最新区块号，支持重试机制
     *
     * <p>功能特性：</p>
     * <ul>
     *   <li>获取链上最新确认的区块号</li>
     *   <li>支持所有EVM兼容链（BSC、ARB、BASE等）</li>
     *   <li>集成重试机制和统一错误处理</li>
     *   <li>返回BigInteger类型的区块号</li>
     * </ul>
     *
     * @param facade EVM配置门面
     * @return 最新区块号
     * @throws WalletException 当获取区块高度失败时抛出异常
     */
    @Retryable(retryFor = {IOException.class, WalletException.class},
        backoff = @Backoff(delay = 500, multiplier = 2.0))
    public BigInteger getLatestBlockNumber(EvmConfigFacade facade) {
        Web3j web3j = configGetWeb3j(facade);

        try {
            log.debug("{}链获取最新区块高度", facade.getChainName());

            // 获取最新区块号
            EthBlockNumber ethBlockNumber = web3j.ethBlockNumber().send();
            BigInteger blockNumber = ethBlockNumber.getBlockNumber();

            log.info("{}链最新区块高度: {}", facade.getChainName(), blockNumber);
            return blockNumber;

        } catch (IOException e) {
            log.error("{}链获取最新区块高度失败: {}", facade.getChainName(), e.getMessage());
            throw new WalletException(facade.getChainName() + "获取最新区块高度失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("{}链获取最新区块高度异常: {}", facade.getChainName(), e.getMessage());
            throw new WalletException(facade.getChainName() + "获取最新区块高度异常: " + e.getMessage());
        }
    }

    /**
     * 获取区块信息
     * 根据区块号获取区块详细信息，包括时间戳
     *
     * <p>功能特性：</p>
     * <ul>
     *   <li>获取指定区块号的详细信息</li>
     *   <li>支持所有EVM兼容链（BSC、ARB、BASE等）</li>
     *   <li>集成重试机制和统一错误处理</li>
     *   <li>返回EthBlock.Block对象，包含时间戳等信息</li>
     * </ul>
     *
     * @param blockNumber 区块号
     * @param facade      EVM配置门面
     * @return 区块信息，如果区块不存在则返回null
     * @throws WalletException 当获取区块信息失败时抛出异常
     */
    @Retryable(retryFor = {IOException.class, WalletException.class},
        backoff = @Backoff(delay = 500, multiplier = 2.0))
    public org.web3j.protocol.core.methods.response.EthBlock.Block getBlockByNumber(BigInteger blockNumber, EvmConfigFacade facade) {
        Web3j web3j = configGetWeb3j(facade);

        try {
            log.debug("{}链获取区块信息: blockNumber={}", facade.getChainName(), blockNumber);

            // 获取区块信息（不包含完整交易信息）
            org.web3j.protocol.core.methods.response.EthBlock ethBlock = web3j
                .ethGetBlockByNumber(org.web3j.protocol.core.DefaultBlockParameter.valueOf(blockNumber), false)
                .send();

            if (ethBlock.getBlock() == null) {
                log.warn("{}链区块{}不存在", facade.getChainName(), blockNumber);
                return null;
            }

            log.debug("{}链成功获取区块信息: blockNumber={}, timestamp={}",
                facade.getChainName(), blockNumber, ethBlock.getBlock().getTimestamp());
            return ethBlock.getBlock();

        } catch (IOException e) {
            log.error("{}链获取区块信息失败: blockNumber={}, error={}",
                facade.getChainName(), blockNumber, e.getMessage());
            throw new WalletException(facade.getChainName() + "获取区块信息失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("{}链获取区块信息异常: blockNumber={}, error={}",
                facade.getChainName(), blockNumber, e.getMessage());
            throw new WalletException(facade.getChainName() + "获取区块信息异常: " + e.getMessage());
        }
    }

    /**
     * 获取交易回执
     * 根据交易哈希获取交易回执，包含gas使用量和手续费信息
     *
     * <p>功能特性：</p>
     * <ul>
     *   <li>获取指定交易的详细回执信息</li>
     *   <li>支持所有EVM兼容链（BSC、ARB、BASE等）</li>
     *   <li>集成重试机制和统一错误处理</li>
     *   <li>返回TransactionReceipt对象，包含gasUsed、effectiveGasPrice等信息</li>
     * </ul>
     *
     * @param transactionHash 交易哈希
     * @param facade          EVM配置门面
     * @return 交易回执，如果交易不存在则返回null
     * @throws WalletException 当获取交易回执失败时抛出异常
     */
    @Retryable(retryFor = {IOException.class, WalletException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2.0, maxDelay = 5000)) // {{ AURA-X: Modify - 优化重试配置：最多3次重试，延迟1-5秒. Approval: 寸止(ID:1678886401). }}
    public org.web3j.protocol.core.methods.response.TransactionReceipt getTransactionReceipt(String transactionHash, EvmConfigFacade facade) {
        Web3j web3j = configGetWeb3j(facade);

        try {
            log.debug("{}链获取交易回执: transactionHash={}", facade.getChainName(), transactionHash);

            // 获取交易回执
            org.web3j.protocol.core.methods.response.EthGetTransactionReceipt ethGetTransactionReceipt = web3j
                .ethGetTransactionReceipt(transactionHash)
                .send();

            if (ethGetTransactionReceipt.getTransactionReceipt().isEmpty()) {
                log.warn("{}链交易{}回执不存在", facade.getChainName(), transactionHash);
                return null;
            }

            org.web3j.protocol.core.methods.response.TransactionReceipt receipt =
                ethGetTransactionReceipt.getTransactionReceipt().get();

            log.debug("{}链成功获取交易回执: transactionHash={}, gasUsed={}, effectiveGasPrice={}",
                facade.getChainName(), transactionHash, receipt.getGasUsed(), receipt.getEffectiveGasPrice());
            return receipt;

        } catch (IOException e) {
            // {{ AURA-X: Modify - 简化错误日志，不打印完整异常堆栈，只记录关键错误信息. Approval: 寸止(ID:1678886401). }}
            log.error("{}链获取交易回执失败: transactionHash={}, error={}",
                facade.getChainName(), transactionHash, e.getMessage());
            throw new WalletException(facade.getChainName() + "获取交易回执失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("{}链获取交易回执异常: transactionHash={}, error={}",
                facade.getChainName(), transactionHash, e.getMessage());
            throw new WalletException(facade.getChainName() + "获取交易回执异常: " + e.getMessage());
        }
    }

    /**
     * 获取交易回执重试失败回调
     * {{ AURA-X: Modify - 添加重试失败回调，记录最终失败信息. Approval: 寸止(ID:1678886401). }}
     */
    @Recover
    public org.web3j.protocol.core.methods.response.TransactionReceipt recoverGetTransactionReceipt(
        Exception ex, String transactionHash, EvmConfigFacade facade) {
        log.error("{}链获取交易回执最终失败: transactionHash={}, 已重试3次, error={}",
            facade.getChainName(), transactionHash, ex.getMessage());
        throw new WalletException(facade.getChainName() + "获取交易回执失败: " + ex.getMessage());
    }

    // ==================== 内部数据类 ====================

    /**
     * 交易状态枚举
     */
    @Getter
    public enum TransactionStatus {
        PENDING("pending", "待确认"),
        SUCCESS("success", "成功"),
        FAILED("failed", "失败"),
        TIMEOUT("timeout", "超时");

        private final String code;
        private final String description;

        TransactionStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

    }

    /**
     * EVM手续费预估结果
     *
     * @param gasPrice          Gas价格（Wei单位）
     * @param gasLimit          Gas限制
     * @param totalGasCost      总Gas费用（Wei单位）
     * @param nativeTokenNeeded 需要的原生代币数量（可读格式）
     */
    public record EvmFeeEstimate(BigInteger gasPrice, BigInteger gasLimit,
                                 BigInteger totalGasCost, BigDecimal nativeTokenNeeded) {
    }

    /**
     * EVM转账结果
     * 封装转账操作的结果信息，包括成功状态、交易哈希、手续费使用情况、验证信息等
     */
    @Data
    public static class EvmTransferResult {
        private final boolean success;
        private final String transactionHash;
        private final boolean feeWalletUsed;
        private final BigDecimal feeProvided;
        private final String errorMessage;

        // 验证相关字段
        private final TransactionStatus status;
        private final Long blockNumber;
        private final Integer confirmations;
        private final BigInteger gasUsed;
        private final BigInteger effectiveGasPrice;

        /**
         * 包级别构造函数（允许在同一个类中使用）
         *
         * @param success           是否成功
         * @param transactionHash   交易哈希
         * @param feeWalletUsed     是否使用了手续费钱包
         * @param feeProvided       提供的手续费数量
         * @param errorMessage      错误消息
         * @param status            交易状态
         * @param blockNumber       区块号
         * @param confirmations     确认数
         * @param gasUsed           实际使用的gas
         * @param effectiveGasPrice 实际gas价格
         */
        EvmTransferResult(boolean success, String transactionHash, boolean feeWalletUsed,
                          BigDecimal feeProvided, String errorMessage, TransactionStatus status,
                          Long blockNumber, Integer confirmations, BigInteger gasUsed,
                          BigInteger effectiveGasPrice) {
            this.success = success;
            this.transactionHash = transactionHash;
            this.feeWalletUsed = feeWalletUsed;
            this.feeProvided = feeProvided;
            this.errorMessage = errorMessage;
            this.status = status;
            this.blockNumber = blockNumber;
            this.confirmations = confirmations;
            this.gasUsed = gasUsed;
            this.effectiveGasPrice = effectiveGasPrice;
        }

        /**
         * 创建成功结果（简化版本，向后兼容）
         *
         * @param transactionHash 交易哈希
         * @param feeWalletUsed   是否使用了手续费钱包
         * @param feeProvided     提供的手续费数量
         * @return 成功结果
         */
        public static EvmTransferResult success(String transactionHash, boolean feeWalletUsed, BigDecimal feeProvided) {
            return new EvmTransferResult(true, transactionHash, feeWalletUsed, feeProvided, null,
                TransactionStatus.SUCCESS, null, null, null, null);
        }

        /**
         * 创建成功结果（完整版本，包含验证信息）
         *
         * @param transactionHash   交易哈希
         * @param feeWalletUsed     是否使用了手续费钱包
         * @param feeProvided       提供的手续费数量
         * @param status            交易状态
         * @param blockNumber       区块号
         * @param confirmations     确认数
         * @param gasUsed           实际使用的gas
         * @param effectiveGasPrice 实际gas价格
         * @return 成功结果
         */
        public static EvmTransferResult success(String transactionHash, boolean feeWalletUsed, BigDecimal feeProvided,
                                                TransactionStatus status, Long blockNumber, Integer confirmations,
                                                BigInteger gasUsed, BigInteger effectiveGasPrice) {
            return new EvmTransferResult(true, transactionHash, feeWalletUsed, feeProvided, null,
                status, blockNumber, confirmations, gasUsed, effectiveGasPrice);
        }

        /**
         * 创建失败结果
         *
         * @param errorMessage 错误消息
         * @return 失败结果
         */
        public static EvmTransferResult failure(String errorMessage) {
            return new EvmTransferResult(false, null, false, BigDecimal.ZERO, errorMessage,
                TransactionStatus.FAILED, null, null, null, null);
        }

        /**
         * 创建超时结果
         *
         * @param transactionHash 交易哈希
         * @param errorMessage    错误消息
         * @return 超时结果
         */
        public static EvmTransferResult timeout(String transactionHash, String errorMessage) {
            return new EvmTransferResult(false, transactionHash, false, BigDecimal.ZERO, errorMessage,
                TransactionStatus.TIMEOUT, null, null, null, null);
        }

    }

    // ==================== 异常识别辅助方法 ====================

    /**
     * 判断是否为余额不足错误
     * 增强版：支持识别ERC-6093标准的ERC20InsufficientBalance错误码
     */
    private boolean isInsufficientBalanceError(Exception e) {
        String message = e.getMessage();
        if (message == null) return false;

        String lowerMessage = message.toLowerCase();

        // 检查ERC-6093标准的ERC20InsufficientBalance错误码 (0xe450d38c)
        if (lowerMessage.contains("0xe450d38c")) {
            return true;
        }

        // 检查传统的余额不足错误信息
        return lowerMessage.contains("insufficient") &&
            (lowerMessage.contains("balance") || lowerMessage.contains("funds")) ||
            lowerMessage.contains("余额不足") ||
            lowerMessage.contains("transfer amount exceeds balance") ||
            lowerMessage.contains("exceeds balance") ||
            lowerMessage.contains("insufficient funds for gas");
    }

    /**
     * 解析ERC20InsufficientBalance错误的详细信息
     * 错误格式：0xe450d38c + 发送方地址(32字节) + 当前余额(32字节) + 尝试转账金额(32字节)
     *
     * @param errorMessage 包含错误码的错误信息
     * @return 格式化的错误描述，如果解析失败则返回简化描述
     */
    private String parseErc20InsufficientBalanceError(String errorMessage) {
        try {
            // 查找错误码位置
            int errorCodeIndex = errorMessage.toLowerCase().indexOf("0xe450d38c");
            if (errorCodeIndex == -1) {
                return "代币余额不足";
            }

            // 提取错误数据部分（去掉0xe450d38c后的128个字符，代表3个32字节参数）
            String errorData = errorMessage.substring(errorCodeIndex + 10); // 跳过"0xe450d38c"
            if (errorData.length() < 192) { // 3 * 64 = 192个十六进制字符
                return "代币余额不足（数据不完整）";
            }

            // 解析参数（每个参数32字节 = 64个十六进制字符）
            String senderHex = errorData.substring(0, 64);
            String currentBalanceHex = errorData.substring(64, 128);
            String attemptedAmountHex = errorData.substring(128, 192);

            // 提取地址（地址在32字节中的后20字节）
            String senderAddress = "0x" + senderHex.substring(24);

            // 转换余额和金额（BigInteger处理大数）
            BigInteger currentBalance = new BigInteger(currentBalanceHex, 16);
            BigInteger attemptedAmount = new BigInteger(attemptedAmountHex, 16);

            // 转换为可读格式（假设18位小数）
            BigDecimal currentBalanceDecimal = new BigDecimal(currentBalance)
                .divide(new BigDecimal("1000000000000000000"), 6, RoundingMode.DOWN);
            BigDecimal attemptedAmountDecimal = new BigDecimal(attemptedAmount)
                .divide(new BigDecimal("1000000000000000000"), 6, RoundingMode.DOWN);

            return String.format("代币余额不足: 地址=%s, 当前余额=%s, 尝试转账=%s",
                senderAddress, currentBalanceDecimal, attemptedAmountDecimal);

        } catch (Exception parseException) {
            log.debug("解析ERC20InsufficientBalance错误失败: {}", parseException.getMessage());
            return "代币余额不足（解析失败）";
        }
    }

    /**
     * 判断是否为网络相关错误
     */
    private boolean isNetworkError(Exception e) {
        String exceptionType = e.getClass().getSimpleName();
        String message = e.getMessage();

        return exceptionType.contains("IOException") ||
            exceptionType.contains("ConnectException") ||
            exceptionType.contains("SocketTimeoutException") ||
            exceptionType.contains("UnknownHostException") ||
            (message != null && (
                message.contains("timeout") ||
                    message.contains("connection") ||
                    message.contains("network") ||
                    message.contains("超时") ||
                    message.contains("网络")
            ));
    }

    /**
     * 判断是否为Gas相关错误
     */
    private boolean isGasRelatedError(Exception e) {
        String message = e.getMessage();
        if (message == null) return false;

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("gas") ||
            lowerMessage.contains("gasprice") ||
            lowerMessage.contains("gaslimit") ||
            lowerMessage.contains("out of gas") ||
            lowerMessage.contains("intrinsic gas too low");
    }

    // ==================== 策略类支持方法 ====================

    /**
     * 估算代币转账手续费 - 为策略类提供支持
     */
    public EvmFeeEstimate estimateTokenTransferFee(String fromAddress, String toAddress,
                                                   BigDecimal amount, String tokenSymbol, EvmConfigFacade facade) {
        Web3j web3j = configGetWeb3j(facade);

        try {
            // 获取gas价格
            EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
            BigInteger gasPrice = ethGasPrice.getGasPrice();

            // 获取代币合约地址和精度
            String contractAddress = facade.getContractAddress(tokenSymbol);
            int decimals = facade.getContractDecimals(tokenSymbol);

            // 转换金额为最小单位
            BigInteger amountRaw = amount.multiply(BigDecimal.TEN.pow(decimals)).toBigInteger();

            // 构建transfer函数调用
            Function function = new Function("transfer",
                Arrays.asList(new Address(toAddress), new Uint256(amountRaw)),
                Collections.emptyList());
            String encodedFunction = FunctionEncoder.encode(function);

            // 估算Gas限制
            BigInteger gasLimit = transferEstimateGasForToken(web3j, fromAddress, contractAddress,
                encodedFunction, facade);

            // 计算总费用（gas价格 * gas限制）
            BigDecimal totalFee = new BigDecimal(gasPrice.multiply(gasLimit))
                .divide(BigDecimal.valueOf(1_000_000_000_000_000_000L), 18, RoundingMode.UP);

            BigInteger totalGasCost = gasPrice.multiply(gasLimit);
            return new EvmFeeEstimate(gasPrice, gasLimit, totalGasCost, totalFee);

        } catch (Exception e) {
            log.error("估算{}代币转账手续费失败", tokenSymbol, e);
            // 返回默认估算值
            BigInteger defaultGasPrice = BigInteger.valueOf(20_000_000_000L); // 20 Gwei
            BigInteger defaultGasLimit = BigInteger.valueOf(100_000L);
            BigDecimal defaultFee = new BigDecimal(defaultGasPrice.multiply(defaultGasLimit))
                .divide(BigDecimal.valueOf(1_000_000_000_000_000_000L), 18, RoundingMode.UP);

            BigInteger defaultTotalGasCost = defaultGasPrice.multiply(defaultGasLimit);
            return new EvmFeeEstimate(defaultGasPrice, defaultGasLimit, defaultTotalGasCost, defaultFee);
        }
    }





    // ==================== 统一确认接口实现 ====================

    @Override
    public TransactionConfirmationResult confirmTransaction(String txHash, TransactionConfirmationConfig config) {
        try {
            log.debug("EVM统一确认开始: txHash={}, config={}", txHash, config.getDescription());

            // 根据配置确定使用哪个EVM链的facade
            EvmConfigFacade facade = determineEvmFacade(config);

            if (facade == null) {
                // {{ AURA-X: Modify - 更明确的错误消息，指导用户正确配置. Approval: 寸止(ID:1722326400). }}
                String errorMsg = config.getChainName() == null || config.getChainName().trim().isEmpty()
                    ? "EVM交易确认必须明确指定链名称(如: BSC, ARB, BASE, ETH)"
                    : "指定的EVM链配置不可用: " + config.getChainName();
                log.error("EVM链配置错误: txHash={}, chainName={}, error={}",
                    txHash, config.getChainName(), errorMsg);
                return TransactionConfirmationResult.failure(txHash,
                    org.dromara.wallet.wallet.transfer.enums.TransactionStatus.UNKNOWN,
                    errorMsg);
            }

            // {{ AURA-X: Modify - 解决循环引用问题，直接调用私有核心方法. Approval: 寸止(ID:1722326400). }}
            // 使用配置参数调用核心确认逻辑，避免调用@Deprecated方法
            EvmTransferResult evmResult = performLightConfirmation(txHash, facade,
                config.getTimeoutSeconds(),
                config.getCheckIntervalSeconds(),
                config.getRequiredConfirmations());

            if (evmResult.isSuccess()) {
                log.debug("EVM统一确认成功: txHash={}, chain={}", txHash, facade.getChainName());
                return TransactionConfirmationResult.successWithBlock(
                    txHash,
                    evmResult.getConfirmations(),
                    evmResult.getBlockNumber(),
                    null // EVM暂不返回区块哈希
                );
            } else {
                log.warn("EVM统一确认失败: txHash={}, chain={}, error={}",
                    txHash, facade.getChainName(), evmResult.getErrorMessage());
                org.dromara.wallet.wallet.transfer.enums.TransactionStatus status =
                    mapEvmStatusToTransactionStatus(evmResult.getStatus());
                return TransactionConfirmationResult.failure(txHash, status, evmResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("EVM统一确认异常: txHash={}, error={}", txHash, e.getMessage(), e);
            return TransactionConfirmationResult.failure(txHash,
                org.dromara.wallet.wallet.transfer.enums.TransactionStatus.UNKNOWN,
                "确认过程异常: " + e.getMessage());
        }
    }

    @Override
    public TransactionConfirmationConfig getDefaultConfirmationConfig() {
        return TransactionConfirmationConfig.evmDefault();
    }

    @Override
    public String getBlockchainType() {
        return "EVM";
    }

    /**
     * 确定使用哪个EVM链的配置门面
     *
     * <p>策略：</p>
     * <ul>
     *   <li>必须在配置中明确指定链名称</li>
     *   <li>不允许使用默认链，避免错误的链选择</li>
     *   <li>如果未指定或指定的链不可用，返回null</li>
     * </ul>
     */
    private EvmConfigFacade determineEvmFacade(TransactionConfirmationConfig config) {
        // 1. 检查是否指定了链名称
        if (config.getChainName() == null || config.getChainName().trim().isEmpty()) {
            log.error("EVM交易确认必须明确指定链名称，当前配置: {}", config.getDescription());
            return null;
        }

        // 2. 使用配置中指定的链名称
        try {
            String chainName = config.getChainName().trim();
            log.debug("使用配置指定的EVM链: {}", chainName);
            return chainConfigFacadeManager.getEvmConfigFacade(chainName);
        } catch (Exception e) {
            log.error("获取配置指定的EVM链({})失败: {}", config.getChainName(), e.getMessage());
            return null;
        }
    }

    /**
     * 将EVM状态映射为统一的交易状态
     */
    private org.dromara.wallet.wallet.transfer.enums.TransactionStatus mapEvmStatusToTransactionStatus(TransactionStatus evmStatus) {
        if (evmStatus == null) {
            return org.dromara.wallet.wallet.transfer.enums.TransactionStatus.UNKNOWN;
        }

        return switch (evmStatus) {
            case SUCCESS -> org.dromara.wallet.wallet.transfer.enums.TransactionStatus.CONFIRMED;
            case FAILED -> org.dromara.wallet.wallet.transfer.enums.TransactionStatus.FAILED;
            case TIMEOUT -> org.dromara.wallet.wallet.transfer.enums.TransactionStatus.TIMEOUT;
            case PENDING -> org.dromara.wallet.wallet.transfer.enums.TransactionStatus.PENDING;
        };
    }


}
