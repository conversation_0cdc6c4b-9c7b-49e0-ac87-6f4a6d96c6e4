package org.dromara.wallet.wallet.transfer.alert;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.wallet.transfer.exception.TronTransferException;
import org.dromara.wallet.wallet.transfer.monitor.TronTransferMonitorReport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * TRON转账告警服务
 *
 * <p>提供TRON转账相关的多渠道告警功能，包括：</p>
 * <ul>
 *   <li>实时异常告警</li>
 *   <li>性能指标告警</li>
 *   <li>多渠道告警发送（钉钉、邮件、日志）</li>
 *   <li>告警频率控制和去重</li>
 *   <li>告警级别自动判定</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Slf4j
@Service
public class TronTransferAlertService {

    /**
     * 告警配置
     */
    @Autowired(required = false)
    private TronTransferAlertConfig alertConfig;

    /**
     * 告警发送器集合
     */
    private final ConcurrentHashMap<String, AlertSender> alertSenders = new ConcurrentHashMap<>();

    /**
     * 告警频率控制 - 记录最后发送时间
     */
    private final ConcurrentHashMap<String, LocalDateTime> lastAlertTime = new ConcurrentHashMap<>();

    /**
     * 异步告警执行器
     */
    private final ScheduledExecutorService alertExecutor = Executors.newScheduledThreadPool(2);

    // ==================== 初始化方法 ====================

    /**
     * 初始化告警发送器
     */
    public void initializeAlertSenders() {
        // 日志告警发送器（默认启用）
        alertSenders.put("log", new LogAlertSender());

        // 钉钉告警发送器
        if (alertConfig != null && alertConfig.isDingTalkEnabled()) {
            alertSenders.put("dingtalk", new DingTalkAlertSender(alertConfig.getDingTalkWebhookUrl()));
        }

        // 邮件告警发送器
        if (alertConfig != null && alertConfig.isEmailEnabled()) {
            alertSenders.put("email", new EmailAlertSender(alertConfig.getEmailConfig()));
        }

        log.info("TRON转账告警服务初始化完成，启用发送器: {}", alertSenders.keySet());
    }

    // ==================== 告警发送方法 ====================

    /**
     * 发送异常告警
     */
    public void sendExceptionAlert(TronTransferException exception, String txHash, String context) {
        if (!isAlertEnabled()) {
            return;
        }

        String alertKey = "exception_" + exception.getErrorType().name();
        if (!shouldSendAlert(alertKey, exception.getAlertLevel())) {
            return;
        }

        TronTransferAlert alert = TronTransferAlert.builder()
                .alertType("EXCEPTION")
                .alertLevel(exception.getAlertLevel())
                .title("TRON转账异常告警")
                .message(buildExceptionAlertMessage(exception, txHash, context))
                .timestamp(LocalDateTime.now())
                .txHash(txHash)
                .exceptionType(exception.getErrorType().name())
                .recoveryAdvice(exception.getRecoveryAdvice())
                .build();

        sendAlertAsync(alert);
        updateLastAlertTime(alertKey);
    }

    /**
     * 发送性能告警
     */
    public void sendPerformanceAlert(TronTransferMonitorReport report) {
        if (!isAlertEnabled() || !report.needsAlert()) {
            return;
        }

        String alertKey = "performance_" + report.getAlertLevel();
        if (!shouldSendAlert(alertKey, report.getAlertLevel())) {
            return;
        }

        TronTransferAlert alert = TronTransferAlert.builder()
                .alertType("PERFORMANCE")
                .alertLevel(report.getAlertLevel())
                .title("TRON转账性能告警")
                .message(report.generateAlertReport())
                .timestamp(LocalDateTime.now())
                .monitorReport(report)
                .build();

        sendAlertAsync(alert);
        updateLastAlertTime(alertKey);
    }

    /**
     * 发送自定义告警
     */
    public void sendCustomAlert(String alertLevel, String title, String message, String context) {
        if (!isAlertEnabled()) {
            return;
        }

        String alertKey = "custom_" + title.hashCode();
        if (!shouldSendAlert(alertKey, alertLevel)) {
            return;
        }

        TronTransferAlert alert = TronTransferAlert.builder()
                .alertType("CUSTOM")
                .alertLevel(alertLevel)
                .title(title)
                .message(message)
                .timestamp(LocalDateTime.now())
                .context(context)
                .build();

        sendAlertAsync(alert);
        updateLastAlertTime(alertKey);
    }

    // ==================== 异步告警处理 ====================

    /**
     * 异步发送告警
     */
    private void sendAlertAsync(TronTransferAlert alert) {
        alertExecutor.submit(() -> {
            try {
                for (AlertSender sender : alertSenders.values()) {
                    try {
                        sender.sendAlert(alert);
                    } catch (Exception e) {
                        log.error("告警发送失败: sender={}, error={}",
                                sender.getClass().getSimpleName(), e.getMessage());
                    }
                }
            } catch (Exception e) {
                log.error("异步告警处理失败: {}", e.getMessage(), e);
            }
        });
    }

    // ==================== 告警控制方法 ====================

    /**
     * 检查是否启用告警
     */
    private boolean isAlertEnabled() {
        return alertConfig != null && alertConfig.isEnabled();
    }

    /**
     * 检查是否应该发送告警（频率控制）
     */
    private boolean shouldSendAlert(String alertKey, String alertLevel) {
        LocalDateTime lastTime = lastAlertTime.get(alertKey);
        if (lastTime == null) {
            return true;
        }

        // 根据告警级别确定最小间隔
        int minIntervalMinutes = getMinIntervalByLevel(alertLevel);
        LocalDateTime threshold = LocalDateTime.now().minusMinutes(minIntervalMinutes);

        return lastTime.isBefore(threshold);
    }

    /**
     * 根据告警级别获取最小间隔
     */
    private int getMinIntervalByLevel(String alertLevel) {
        return switch (alertLevel.toUpperCase()) {
            case "CRITICAL" -> 5;   // 严重告警：5分钟间隔
            case "ERROR" -> 15;     // 错误告警：15分钟间隔
            case "WARN" -> 30;      // 警告告警：30分钟间隔
            default -> 60;          // 信息告警：60分钟间隔
        };
    }

    /**
     * 更新最后告警时间
     */
    private void updateLastAlertTime(String alertKey) {
        lastAlertTime.put(alertKey, LocalDateTime.now());
    }

    // ==================== 消息构建方法 ====================

    /**
     * 构建异常告警消息
     */
    private String buildExceptionAlertMessage(TronTransferException exception, String txHash, String context) {
        StringBuilder message = new StringBuilder();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        message.append("🚨 TRON转账异常告警\n");
        message.append("时间: ").append(LocalDateTime.now().format(formatter)).append("\n");
        message.append("异常类型: ").append(exception.getErrorType().getDescription()).append("\n");
        message.append("错误信息: ").append(exception.getMessage()).append("\n");

        if (txHash != null) {
            message.append("交易哈希: ").append(txHash).append("\n");
        }

        if (context != null) {
            message.append("上下文: ").append(context).append("\n");
        }

        message.append("恢复建议: ").append(exception.getRecoveryAdvice()).append("\n");
        message.append("可重试: ").append(exception.isRetryable() ? "是" : "否").append("\n");

        if (exception.isRetryable()) {
            message.append("最大重试次数: ").append(exception.getMaxRetries()).append("\n");
        }

        return message.toString();
    }

    // ==================== 告警发送器接口 ====================

    /**
     * 告警发送器接口
     */
    public interface AlertSender {
        void sendAlert(TronTransferAlert alert) throws Exception;
    }

    /**
     * 日志告警发送器
     */
    private static class LogAlertSender implements AlertSender {
        @Override
        public void sendAlert(TronTransferAlert alert) {
            switch (alert.getAlertLevel().toUpperCase()) {
                case "CRITICAL" -> log.error("[CRITICAL] {}: {}", alert.getTitle(), alert.getMessage());
                case "ERROR" -> log.error("[ERROR] {}: {}", alert.getTitle(), alert.getMessage());
                case "WARN" -> log.warn("[WARN] {}: {}", alert.getTitle(), alert.getMessage());
                default -> log.info("[INFO] {}: {}", alert.getTitle(), alert.getMessage());
            }
        }
    }

    /**
     * 钉钉告警发送器
     */
    private static class DingTalkAlertSender implements AlertSender {
        private final String webhookUrl;

        public DingTalkAlertSender(String webhookUrl) {
            this.webhookUrl = webhookUrl;
        }

        @Override
        public void sendAlert(TronTransferAlert alert) throws Exception {
            // TODO: 实现钉钉机器人消息发送
            log.info("钉钉告警发送: {}", alert.getTitle());
        }
    }

    /**
     * 邮件告警发送器
     */
    private static class EmailAlertSender implements AlertSender {
        private final Object emailConfig;

        public EmailAlertSender(Object emailConfig) {
            this.emailConfig = emailConfig;
        }

        @Override
        public void sendAlert(TronTransferAlert alert) throws Exception {
            // TODO: 实现邮件告警发送
            log.info("邮件告警发送: {}", alert.getTitle());
        }
    }

    // ==================== 清理方法 ====================

    /**
     * 清理过期的告警记录
     */
    public void cleanupExpiredAlerts() {
        LocalDateTime threshold = LocalDateTime.now().minusHours(24);
        lastAlertTime.entrySet().removeIf(entry -> entry.getValue().isBefore(threshold));
        log.debug("清理过期告警记录，当前记录数: {}", lastAlertTime.size());
    }

    /**
     * 关闭告警服务
     */
    public void shutdown() {
        alertExecutor.shutdown();
        try {
            if (!alertExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                alertExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            alertExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("TRON转账告警服务已关闭");
    }

    // ==================== 告警数据类 ====================

    /**
     * TRON转账告警数据类
     */
    @Data
    @Builder
    public static class TronTransferAlert {
        /**
         * 告警类型（EXCEPTION/PERFORMANCE/CUSTOM）
         */
        private String alertType;

        /**
         * 告警级别（INFO/WARN/ERROR/CRITICAL）
         */
        private String alertLevel;

        /**
         * 告警标题
         */
        private String title;

        /**
         * 告警消息
         */
        private String message;

        /**
         * 告警时间
         */
        private LocalDateTime timestamp;

        /**
         * 交易哈希（可选）
         */
        private String txHash;

        /**
         * 异常类型（可选）
         */
        private String exceptionType;

        /**
         * 恢复建议（可选）
         */
        private String recoveryAdvice;

        /**
         * 监控报告（可选）
         */
        private TronTransferMonitorReport monitorReport;

        /**
         * 上下文信息（可选）
         */
        private String context;
    }
}
