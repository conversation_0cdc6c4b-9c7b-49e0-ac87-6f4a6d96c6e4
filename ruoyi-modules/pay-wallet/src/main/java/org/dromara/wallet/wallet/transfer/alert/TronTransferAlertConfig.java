package org.dromara.wallet.wallet.transfer.alert;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * TRON转账告警配置
 *
 * <p>配置TRON转账告警的各项参数，包括：</p>
 * <ul>
 *   <li>告警开关和级别控制</li>
 *   <li>告警渠道配置（钉钉、邮件等）</li>
 *   <li>告警频率和阈值设置</li>
 *   <li>性能监控告警规则</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Data
@Component
@ConfigurationProperties(prefix = "tron.transfer.alert")
public class TronTransferAlertConfig {

    // ==================== 基础配置 ====================

    /**
     * 是否启用告警
     */
    private boolean enabled = false;

    /**
     * 最小告警级别（INFO/WARN/ERROR/CRITICAL）
     */
    private String minAlertLevel = "WARN";

    /**
     * 告警发送间隔（分钟）
     */
    private int alertIntervalMinutes = 30;

    /**
     * 是否启用异常告警
     */
    private boolean exceptionAlertEnabled = true;

    /**
     * 是否启用性能告警
     */
    private boolean performanceAlertEnabled = true;

    // ==================== 性能告警阈值 ====================

    /**
     * 失败率告警阈值（百分比）
     */
    private double failureRateThreshold = 10.0;

    /**
     * 转账耗时告警阈值（毫秒）
     */
    private long transferTimeThreshold = 30000;

    /**
     * 确认耗时告警阈值（毫秒）
     */
    private long confirmationTimeThreshold = 60000;

    /**
     * 异常频率告警阈值（百分比）
     */
    private double exceptionRateThreshold = 15.0;

    /**
     * 最小样本数（低于此数量不触发告警）
     */
    private int minSampleSize = 10;

    // ==================== 钉钉告警配置 ====================

    /**
     * 是否启用钉钉告警
     */
    private boolean dingTalkEnabled = false;

    /**
     * 钉钉机器人Webhook URL
     */
    private String dingTalkWebhookUrl;

    /**
     * 钉钉机器人密钥
     */
    private String dingTalkSecret;

    /**
     * 钉钉消息@所有人
     */
    private boolean dingTalkAtAll = false;

    /**
     * 钉钉消息@指定人员手机号
     */
    private String[] dingTalkAtMobiles;

    // ==================== 邮件告警配置 ====================

    /**
     * 是否启用邮件告警
     */
    private boolean emailEnabled = false;

    /**
     * 邮件配置
     */
    private EmailConfig emailConfig = new EmailConfig();

    /**
     * 邮件配置类
     */
    @Data
    public static class EmailConfig {
        /**
         * SMTP服务器地址
         */
        private String smtpHost;

        /**
         * SMTP端口
         */
        private int smtpPort = 587;

        /**
         * 发送者邮箱
         */
        private String fromEmail;

        /**
         * 发送者密码
         */
        private String fromPassword;

        /**
         * 接收者邮箱列表
         */
        private String[] toEmails;

        /**
         * 是否启用TLS
         */
        private boolean tlsEnabled = true;
    }

    // ==================== 短信告警配置 ====================

    /**
     * 是否启用短信告警
     */
    private boolean smsEnabled = false;

    /**
     * 短信配置
     */
    private SmsConfig smsConfig = new SmsConfig();

    /**
     * 短信配置类
     */
    @Data
    public static class SmsConfig {
        /**
         * 短信服务商类型（aliyun/tencent）
         */
        private String provider = "aliyun";

        /**
         * AccessKey ID
         */
        private String accessKeyId;

        /**
         * AccessKey Secret
         */
        private String accessKeySecret;

        /**
         * 短信签名
         */
        private String signName;

        /**
         * 短信模板代码
         */
        private String templateCode;

        /**
         * 接收短信的手机号列表
         */
        private String[] phoneNumbers;
    }

    // ==================== 高级配置 ====================

    /**
     * 告警去重时间窗口（分钟）
     */
    private int deduplicationWindowMinutes = 60;

    /**
     * 最大告警队列大小
     */
    private int maxAlertQueueSize = 1000;

    /**
     * 告警发送超时时间（秒）
     */
    private int alertSendTimeoutSeconds = 30;

    /**
     * 是否启用告警统计
     */
    private boolean alertStatisticsEnabled = true;

    /**
     * 告警统计保留天数
     */
    private int alertStatisticsRetentionDays = 30;

    // ==================== 验证方法 ====================

    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        if (!enabled) {
            return true; // 未启用时认为配置有效
        }

        // 检查钉钉配置
        if (dingTalkEnabled && (dingTalkWebhookUrl == null || dingTalkWebhookUrl.trim().isEmpty())) {
            return false;
        }

        // 检查邮件配置
        if (emailEnabled && !isEmailConfigValid()) {
            return false;
        }

        // 检查短信配置
        if (smsEnabled && !isSmsConfigValid()) {
            return false;
        }

        return true;
    }

    /**
     * 验证邮件配置
     */
    private boolean isEmailConfigValid() {
        return emailConfig != null &&
               emailConfig.getSmtpHost() != null &&
               emailConfig.getFromEmail() != null &&
               emailConfig.getFromPassword() != null &&
               emailConfig.getToEmails() != null &&
               emailConfig.getToEmails().length > 0;
    }

    /**
     * 验证短信配置
     */
    private boolean isSmsConfigValid() {
        return smsConfig != null &&
               smsConfig.getAccessKeyId() != null &&
               smsConfig.getAccessKeySecret() != null &&
               smsConfig.getSignName() != null &&
               smsConfig.getTemplateCode() != null &&
               smsConfig.getPhoneNumbers() != null &&
               smsConfig.getPhoneNumbers().length > 0;
    }

    /**
     * 检查告警级别是否满足最小要求
     */
    public boolean shouldAlert(String alertLevel) {
        if (!enabled) {
            return false;
        }

        int currentLevel = getAlertLevelValue(alertLevel);
        int minLevel = getAlertLevelValue(minAlertLevel);
        
        return currentLevel >= minLevel;
    }

    /**
     * 获取告警级别数值
     */
    private int getAlertLevelValue(String level) {
        return switch (level.toUpperCase()) {
            case "INFO" -> 1;
            case "WARN" -> 2;
            case "ERROR" -> 3;
            case "CRITICAL" -> 4;
            default -> 0;
        };
    }

    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("TRON转账告警配置: ");
        summary.append("enabled=").append(enabled);
        
        if (enabled) {
            summary.append(", minLevel=").append(minAlertLevel);
            summary.append(", channels=[");
            
            if (dingTalkEnabled) summary.append("钉钉,");
            if (emailEnabled) summary.append("邮件,");
            if (smsEnabled) summary.append("短信,");
            
            if (summary.charAt(summary.length() - 1) == ',') {
                summary.setLength(summary.length() - 1);
            }
            summary.append("]");
            
            summary.append(", failureThreshold=").append(failureRateThreshold).append("%");
            summary.append(", timeThreshold=").append(transferTimeThreshold).append("ms");
        }
        
        return summary.toString();
    }
}
