package org.dromara.wallet.wallet.monitor.tron.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.scanning.chain.model.tron.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TransactionInfo on the Tron chain
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MyTronTransactionModel extends TronTransactionModel {


    // ------------ Block information, used for developers to obtain in the listener -------------

    @JsonIgnore
    private String blockID;

    @JsonIgnore
    private TronBlockHeaderModel tronBlockHeaderModel;

    // ------------ 事件日志信息，通过额外API查询获取 -------------

    /**
     * 交易的事件日志列表
     * 需要通过getTransactionInfoById API额外查询获取
     */
    @JsonIgnore
    private List<TronLogModel> logs;

    /**
     * 是否已加载事件日志
     */
    @JsonIgnore
    private boolean logsLoaded = false;

    // ------------ 事件日志相关方法 -------------

    /**
     * 设置事件日志列表
     */
    public void setLogs(List<TronLogModel> logs) {
        this.logs = logs;
        this.logsLoaded = true;
    }

    /**
     * 获取所有Transfer事件
     */
    public List<TronLogModel> getTransferEvents() {
        if (logs == null) {
            return List.of();
        }
        return logs.stream()
            .filter(TronLogModel::isTransferEvent)
            .collect(Collectors.toList());
    }

    /**
     * 检查交易是否包含Transfer事件
     */
    public boolean hasTransferEvents() {
        return logs != null && logs.stream().anyMatch(TronLogModel::isTransferEvent);
    }

    /**
     * 获取涉及指定地址的Transfer事件
     */
    public List<TronLogModel> getTransferEventsForAddress(String address) {
        if (logs == null || address == null) {
            return List.of();
        }
        return logs.stream()
            .filter(TronLogModel::isTransferEvent)
            .filter(log -> log.containsAddress(address))
            .collect(Collectors.toList());
    }

    /**
     * 检查交易是否涉及指定地址的Transfer事件
     */
    public boolean hasTransferEventsForAddress(String address) {
        return !getTransferEventsForAddress(address).isEmpty();
    }

    // ------------ 扩展字段，用于新的扫描架构 -------------

    /**
     * 链名称
     */
    @JsonIgnore
    private String chainName;

    /**
     * 合约地址
     */
    @JsonIgnore
    private String contractAddress;

    /**
     * 发送方地址
     */
    @JsonIgnore
    private String fromAddress;

    /**
     * 接收方地址
     */
    @JsonIgnore
    private String toAddress;

    /**
     * 原始金额（未转换精度）
     */
    @JsonIgnore
    private String rawAmount;

    /**
     * 转换后的金额
     */
    @JsonIgnore
    private BigDecimal amount;

    /**
     * 代币符号
     */
    @JsonIgnore
    private String tokenSymbol;

    /**
     * 代币精度
     */
    @JsonIgnore
    private Integer tokenDecimals;

    /**
     * 交易类型（receive/send/collect/unknown）
     */
    @JsonIgnore
    private String transactionType;

    /**
     * 处理状态（0-未处理，1-成功，2-失败）
     */
    @JsonIgnore
    private Integer processStatus = 0;

    /**
     * 时间戳
     */
    @JsonIgnore
    private Long timestamp;

    /**
     * 错误信息
     */
    @JsonIgnore
    private String errorMessage;

    /**
     * 交易手续费
     */
    @JsonIgnore
    private BigDecimal transactionFee;

    /**
     * 区块号
     */
    @JsonIgnore
    private java.math.BigInteger blockNumber;

    /**
     * 重试次数
     * 记录当前交易已经重试的次数
     */
    @JsonIgnore
    private Integer retryCount = 0;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Long createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Long updateTime;

    /**
     * 是否可信
     * 用于标识交易的可信度
     */
    @JsonIgnore
    private Boolean trustworthy = true;

    // ------------ 便捷方法 -------------

    /**
     * 检查是否已处理
     */
    public boolean isProcessed() {
        return processStatus != null && processStatus == 1;
    }

    /**
     * 检查是否处理失败
     */
    public boolean isFailed() {
        return processStatus != null && processStatus == 2;
    }

    /**
     * 检查是否为接收交易
     */
    public boolean isReceiveTransaction() {
        return "receive".equals(this.transactionType);
    }

    /**
     * 检查是否为发送交易
     */
    public boolean isSendTransaction() {
        return "send".equals(this.transactionType);
    }

    /**
     * 检查是否为归集交易
     */
    public boolean isCollectTransaction() {
        return "collect".equals(this.transactionType);
    }

    /**
     * 获取格式化的交易信息
     */
    public String getFormattedInfo() {
        return String.format("txHash=%s, from=%s, to=%s, amount=%s %s, contract=%s",
            super.getTxID(), fromAddress, toAddress, amount, tokenSymbol, contractAddress);
    }

}
