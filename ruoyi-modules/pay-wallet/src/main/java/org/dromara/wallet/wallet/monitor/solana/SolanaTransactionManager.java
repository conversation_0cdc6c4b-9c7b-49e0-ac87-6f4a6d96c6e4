package org.dromara.wallet.wallet.monitor.solana;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.domain.bo.MetaSolanaTransactionBo;
import org.dromara.wallet.service.IMetaSolanaTransactionsService;
import org.dromara.wallet.wallet.helper.SolanaHelper;
import org.dromara.wallet.wallet.monitor.solana.dto.SolanaTransactionModel;

import org.dromara.wallet.wallet.monitor.solana.event.TransactionEvent;
import org.p2p.solanaj.rpc.types.ConfirmedTransaction;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.dromara.wallet.wallet.monitor.solana.processor.SolanaProcessorChain;

import java.util.List;

/**
 * sol交易记录处理
 *
 * <AUTHOR>
 * @date 2025/4/27 14:57
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaTransactionManager {

    private final SolanaHelper solanaHelper;
    private final IMetaSolanaTransactionsService solanaTransactionsService;

    // 处理器链
    private final SolanaProcessorChain processorChain;

    /**
     * 处理交易事件 - 使用处理器链模式
     * 统一处理所有类型的交易事件
     */
    @EventListener
    public void handleTransactionEvent(TransactionEvent event) {
        TenantHelper.setDynamic("000000", true);
        String signature = event.getSignature();
        String address = event.getAddress();

        log.info("处理交易事件：签名={}, 关联地址={}", signature, address);

        try {
            // 使用处理器链模式处理交易
            processTransactionWithChain(signature, address);

        } catch (Exception e) {
            log.error("处理交易事件失败：签名={}, 地址={}, 错误: {}", signature, address, e.getMessage());
        }
    }

    /**
     * 使用处理器链模式处理交易
     *
     * @param signature 交易签名
     * @param address   关联地址（可为null）
     */
    private void processTransactionWithChain(String signature, String address) {
        // 检查交易是否已处理
        if (solanaTransactionsService.isExist(signature)) {
            log.debug("该交易已处理:{}", signature);
            return;
        }

        // 获取交易详情
        ConfirmedTransaction transaction = solanaHelper.getTransaction(signature);
        if (transaction == null) {
            log.warn("无法获取交易详情: {}", signature);
            return;
        }

        // 构建交易模型
        SolanaTransactionModel solTransactionModel = SolanaTransactionModel.builder()
            .setSolBlock(solanaHelper.getSlotContent(transaction.getSlot()))
            .setTransactionObject(transaction)
            .build();

        // {{ AURA-X: Modify - 使用统一的处理器链执行所有处理步骤，简化代码逻辑. Approval: 寸止(ID:1678886400). }}
        // 执行处理器链
        boolean success = processorChain.execute(solTransactionModel, address);

        if (success) {
            log.info("交易{}处理完成", signature);
        } else {
            log.debug("交易{}处理被中断", signature);
        }
    }

}
