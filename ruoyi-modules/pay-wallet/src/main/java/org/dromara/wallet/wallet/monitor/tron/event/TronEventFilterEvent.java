package org.dromara.wallet.wallet.monitor.tron.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.tron.TronLogModel;
import org.dromara.common.scanning.monitor.TronMonitorEvent;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.wallet.monitor.tron.utils.TronEventLogUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * TRON事件过滤事件
 * 负责过滤Transfer事件并按监控合约地址筛选
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>Transfer事件过滤：只保留Transfer类型的事件日志</li>
 *   <li>合约地址过滤：只保留监控合约地址的事件</li>
 *   <li>数据清洗：移除不相关的事件日志</li>
 *   <li>性能优化：减少后续处理的数据量</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TronEventFilterEvent implements TronMonitorEvent {

    private final TronConfigFacade configFacade;



    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 检查是否为TRON交易模型
            if (transactionModel.getTronTransactionModel() == null) {
                log.debug("{}链跳过非TRON交易模型", configFacade.getChainName());
                return;
            }

            // 检查是否为TRON交易模型
            if (transactionModel.getTronTransactionModel() == null) {
                log.debug("{}链跳过非TRON交易模型", configFacade.getChainName());
                return;
            }

            String txId = transactionModel.getTronTransactionModel().getTxID();
            if (txId == null) {
                log.debug("{}链交易ID为空，跳过事件过滤", configFacade.getChainName());
                return;
            }

            // 获取解析后的日志
            List<TronLogModel> parsedLogs = TronTransactionDetailEvent.getParsedLogs(transactionModel);
            if (parsedLogs.isEmpty()) {
                log.debug("{}链交易{}没有解析后的日志，跳过事件过滤", configFacade.getChainName(), txId);
                storeFilteredLogs(transactionModel, new ArrayList<>());
                return;
            }

            // 执行事件过滤
            List<TronLogModel> filteredLogs = filterEvents(parsedLogs, txId);

            // 存储过滤后的日志供后续Event使用
            storeFilteredLogs(transactionModel, filteredLogs);

            if (!filteredLogs.isEmpty()) {
                log.debug("{}链交易{}过滤后获得{}个相关Transfer事件",
                    configFacade.getChainName(), txId, filteredLogs.size());
            } else {
                log.debug("{}链交易{}过滤后没有相关的Transfer事件", configFacade.getChainName(), txId);
            }

        } catch (Exception e) {
            log.error("{}链事件过滤失败: {}", configFacade.getChainName(), e.getMessage());
            // 异常时存储空列表，避免后续Event出错
            storeFilteredLogs(transactionModel, new ArrayList<>());
        }
    }

    /**
     * 过滤事件日志
     */
    private List<TronLogModel> filterEvents(List<TronLogModel> logs, String txId) {
        if (logs.isEmpty()) {
            return new ArrayList<>();
        }

        // 1. 过滤Transfer事件
        List<TronLogModel> transferEvents = TronEventLogUtils.filterTransferEvents(logs);
        if (transferEvents.isEmpty()) {
            log.debug("{}链交易{}没有Transfer事件", configFacade.getChainName(), txId);
            return new ArrayList<>();
        }

        // 2. 进一步过滤监控合约地址
        Set<String> monitoredContractAddresses = getMonitoredContractAddresses();
        if (monitoredContractAddresses.isEmpty()) {
            log.debug("{}链没有配置监控合约地址，返回所有Transfer事件", configFacade.getChainName());
            return transferEvents;
        }

        List<TronLogModel> filteredLogs = filterByMonitoredContracts(transferEvents, monitoredContractAddresses);

        log.debug("{}链交易{}：原始日志{}个 -> Transfer事件{}个 -> 监控合约过滤后{}个",
            configFacade.getChainName(), txId, logs.size(), transferEvents.size(), filteredLogs.size());

        return filteredLogs;
    }

    /**
     * 根据监控合约地址过滤事件日志
     */
    private List<TronLogModel> filterByMonitoredContracts(List<TronLogModel> logs, Set<String> monitoredContractAddresses) {
        if (logs.isEmpty() || monitoredContractAddresses.isEmpty()) {
            return new ArrayList<>();
        }

        List<TronLogModel> filteredLogs = new ArrayList<>();
        for (TronLogModel log : logs) {
            String contractAddress = log.getAddress();
            if (contractAddress != null && monitoredContractAddresses.contains(contractAddress.toLowerCase())) {
                filteredLogs.add(log);
            }
        }
        return filteredLogs;
    }

    /**
     * 获取监控的合约地址集合
     */
    private Set<String> getMonitoredContractAddresses() {
        return configFacade.getEnabledContractAddresses().stream()
            .map(String::toLowerCase)
            .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 存储过滤后的日志
     */
    private void storeFilteredLogs(TransactionModel transactionModel, List<TronLogModel> filteredLogs) {
        if (transactionModel == null) {
            return;
        }

        // 初始化处理上下文（如果不存在）
        if (transactionModel.getProcessingContext() == null) {
            transactionModel.setProcessingContext(new java.util.concurrent.ConcurrentHashMap<>());
        }

        // 存储过滤后的日志到处理上下文中
        transactionModel.getProcessingContext().put("TRON_FILTERED_LOGS", filteredLogs);

        log.trace("{}链过滤后的日志数量: {}", configFacade.getChainName(), filteredLogs.size());
    }

    /**
     * 获取过滤后的日志（供后续Event使用）
     */
    @SuppressWarnings("unchecked")
    public static List<TronLogModel> getFilteredLogs(TransactionModel transactionModel) {
        if (transactionModel == null || transactionModel.getProcessingContext() == null) {
            return new ArrayList<>();
        }

        Object filteredLogs = transactionModel.getProcessingContext().get("TRON_FILTERED_LOGS");
        if (filteredLogs instanceof List) {
            try {
                return (List<TronLogModel>) filteredLogs;
            } catch (ClassCastException e) {
                log.warn("获取过滤后的日志时类型转换失败: {}", e.getMessage());
                return new ArrayList<>();
            }
        }

        return new ArrayList<>();
    }
}
