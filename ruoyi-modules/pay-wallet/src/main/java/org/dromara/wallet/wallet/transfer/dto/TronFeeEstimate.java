package org.dromara.wallet.wallet.transfer.dto;

import java.math.BigDecimal;

/**
 * TRON手续费估算实现
 * 
 * <p>封装TRON转账的手续费估算信息，包括：</p>
 * <ul>
 *   <li>Energy需求量</li>
 *   <li>Bandwidth需求量</li>
 *   <li>等价TRX需求量</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public record TronFeeEstimate(
    long energyNeeded,
    long bandwidthNeeded,
    BigDecimal trxNeeded
) implements FeeEstimate {

    @Override
    public BigDecimal getNativeTokenNeeded() {
        return trxNeeded;
    }

    @Override
    public String getNativeTokenSymbol() {
        return "TRX";
    }

    @Override
    public String getDescription() {
        return String.format("Energy: %d, Bandwidth: %d, TRX: %s", 
            energyNeeded, bandwidthNeeded, trxNeeded);
    }

    /**
     * 创建TRX转账的手续费估算
     */
    public static TronFeeEstimate forTrxTransfer(long bandwidthNeeded, BigDecimal trxNeeded) {
        return new TronFeeEstimate(0, bandwidthNeeded, trxNeeded);
    }

    /**
     * 创建TRC20代币转账的手续费估算
     */
    public static TronFeeEstimate forTrc20Transfer(long energyNeeded, long bandwidthNeeded, BigDecimal trxNeeded) {
        return new TronFeeEstimate(energyNeeded, bandwidthNeeded, trxNeeded);
    }
}
