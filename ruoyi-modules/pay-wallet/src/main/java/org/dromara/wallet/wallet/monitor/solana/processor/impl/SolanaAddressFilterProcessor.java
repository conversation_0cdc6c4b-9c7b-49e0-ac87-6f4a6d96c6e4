package org.dromara.wallet.wallet.monitor.solana.processor.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.service.IMetaSolanaCstaddressinfoService;
import org.dromara.wallet.wallet.monitor.solana.dto.SolanaTransactionModel;
import org.dromara.wallet.wallet.monitor.solana.processor.SolanaTransactionProcessor;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * Solana地址过滤处理器
 * 
 * <p>功能：</p>
 * <ul>
 *   <li>检查交易是否涉及监控的钱包地址</li>
 *   <li>提前过滤无关交易，提高处理性能</li>
 *   <li>支持发送方和接收方地址检查</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaAddressFilterProcessor implements SolanaTransactionProcessor {

    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;

    @Override
    public boolean process(SolanaTransactionModel transactionModel, String userAddress) {
        try {
            // 获取监控的地址集合
            Set<String> monitoredAddresses = solanaCstaddressinfoService.queryAllAddressSet();
            
            if (monitoredAddresses.isEmpty()) {
                log.debug("没有配置监控地址，跳过地址过滤");
                return false;
            }

            // 如果指定了用户地址，优先检查
            if (userAddress != null && !userAddress.isEmpty()) {
                boolean isMonitored = monitoredAddresses.contains(userAddress);
                log.debug("用户指定地址{}{}在监控列表中", userAddress, isMonitored ? "" : "不");
                return isMonitored;
            }

            // 检查交易中的所有地址变化（如果已解析）
            if (transactionModel.getAddressChanges() != null) {
                boolean hasMonitoredAddress = transactionModel.getAddressChanges().keySet().stream()
                    .anyMatch(monitoredAddresses::contains);

                if (hasMonitoredAddress) {
                    log.debug("交易涉及监控地址，继续处理");
                    return true;
                } else {
                    log.debug("交易不涉及任何监控地址，跳过处理");
                    return false;
                }
            }

            // 如果还没有解析地址变化，暂时通过过滤，让后续处理器处理
            log.debug("交易尚未解析地址变化，暂时通过地址过滤");
            return true;

        } catch (Exception e) {
            log.error("Solana地址过滤失败: {}", e.getMessage());
            // 异常时保守处理，继续后续流程
            return true;
        }
    }

    @Override
    public String getProcessorName() {
        return "SolanaAddressFilterProcessor";
    }

    @Override
    public int getOrder() {
        return 100; // 第一个执行
    }

    /**
     * 检查指定地址是否在监控列表中
     *
     * @param address 要检查的地址
     * @return 是否在监控列表中
     */
    public boolean isMonitoredAddress(String address) {
        if (address == null || address.isEmpty()) {
            return false;
        }

        try {
            Set<String> monitoredAddresses = solanaCstaddressinfoService.queryAllAddressSet();
            return monitoredAddresses.contains(address);
        } catch (Exception e) {
            log.error("检查监控地址失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取交易中涉及的监控地址列表
     *
     * @param transactionModel Solana交易模型
     * @return 涉及的监控地址集合
     */
    public Set<String> getInvolvedMonitoredAddresses(SolanaTransactionModel transactionModel) {
        try {
            Set<String> monitoredAddresses = solanaCstaddressinfoService.queryAllAddressSet();
            
            if (transactionModel.getAddressChanges() == null) {
                return Set.of();
            }
            
            return transactionModel.getAddressChanges().keySet().stream()
                .filter(monitoredAddresses::contains)
                .collect(java.util.stream.Collectors.toSet());
                
        } catch (Exception e) {
            log.error("获取涉及的监控地址失败: {}", e.getMessage());
            return Set.of();
        }
    }
}
