package org.dromara.wallet.wallet.monitor.tron.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.monitor.TronMonitorEvent;

import org.dromara.wallet.config.facade.TronConfigFacade;
import org.redisson.api.RBucket;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * TRON扫描进度保存事件
 * 专门负责保存TRON链扫描进度到Redis，从业务处理事件中分离出来
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>扫描进度持久化：将当前扫描的区块号保存到Redis</li>
 *   <li>防重复保存：相同区块号不重复保存，提高性能</li>
 *   <li>错误处理：保存失败时记录错误但不影响业务处理</li>
 *   <li>可选择性：手动扫描时可以不使用此事件，避免进度覆盖</li>
 * </ul>
 *
 * <p>设计目的：</p>
 * <ul>
 *   <li>解决手动扫描可能覆盖正常扫描进度的问题</li>
 *   <li>将进度保存逻辑从业务处理中分离，提高代码清晰度</li>
 *   <li>为不同类型的扫描（正常扫描vs手动扫描）提供灵活性</li>
 * </ul>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>正常的TRON链扫描：需要保存进度</li>
 *   <li>手动TRON区块扫描：不需要保存进度，避免覆盖</li>
 *   <li>补偿扫描：根据需要决定是否保存进度</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TronScanProgressEvent implements TronMonitorEvent {

    private final TronConfigFacade tronConfigFacade;

    /**
     * TRON链Redis存储键名
     */
    private static final String TRON_CURRENT_BLOCK_KEY = "tron_current_block";

    /**
     * 最后保存的区块号缓存
     * 用于避免重复保存相同的区块号
     */
    private final AtomicReference<BigInteger> lastSavedBlock = new AtomicReference<>();



    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 1. 检查是否为TRON交易模型
            if (transactionModel.getTronTransactionModel() == null) {
                return;
            }

            // 2. 检查链类型
            String chainType = transactionModel.getChainType();
            if (!"TRON".equalsIgnoreCase(chainType)) {
                log.debug("非TRON链交易，跳过进度保存: {}", chainType);
                return;
            }

            // 3. 获取区块号
            BigInteger blockNumber = getBlockNumber(transactionModel);
            if (blockNumber == null) {
                log.debug("TRON链无法获取区块号，跳过进度保存");
                return;
            }

            // 4. 保存扫描进度
            saveProgressIfNeeded(blockNumber);

        } catch (Exception e) {
            // 进度保存失败不应该影响业务处理，只记录错误
            log.error("TRON链扫描进度保存失败: blockNumber={}, error={}",
                getBlockNumber(transactionModel), e.getMessage());
        }
    }

    /**
     * 保存扫描进度（如果需要的话）
     * 避免重复保存相同的区块号
     */
    private void saveProgressIfNeeded(BigInteger blockNumber) {
        try {
            // 检查是否需要保存（避免重复保存相同区块号）
            BigInteger lastSaved = lastSavedBlock.get();
            if (lastSaved != null && lastSaved.compareTo(blockNumber) >= 0) {
                // 当前区块号不大于已保存的区块号，跳过保存
                return;
            }

            // 保存到Redis
            RBucket<BigInteger> bucket = RedisUtils.getClient().getBucket(TRON_CURRENT_BLOCK_KEY);
            bucket.set(blockNumber);

            // 更新缓存
            lastSavedBlock.set(blockNumber);

            log.debug("TRON链扫描进度已保存: {}", blockNumber);

        } catch (Exception e) {
            log.error("TRON链扫描进度保存到Redis失败: blockNumber={}, error={}",
                blockNumber, e.getMessage());
        }
    }

    /**
     * 从交易模型中获取区块号
     */
    private BigInteger getBlockNumber(TransactionModel transactionModel) {
        try {
            if (transactionModel.getTronTransactionModel() != null &&
                transactionModel.getTronTransactionModel().getTronBlockHeaderModel() != null &&
                transactionModel.getTronTransactionModel().getTronBlockHeaderModel().getTronRawDataModel() != null) {
                return transactionModel.getTronTransactionModel().getTronBlockHeaderModel().getTronRawDataModel().getNumber();
            }
        } catch (Exception e) {
            log.debug("获取TRON区块号失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 手动清除进度缓存
     * 用于重置或强制更新进度
     */
    public void clearProgressCache() {
        lastSavedBlock.set(null);
        log.info("TRON链扫描进度缓存已清除");
    }

    /**
     * 获取最后保存的区块号
     */
    public BigInteger getLastSavedBlock() {
        return lastSavedBlock.get();
    }

    /**
     * 手动设置TRON链的进度
     * 用于初始化或强制设置进度
     */
    public void setProgress(BigInteger blockNumber) {
        try {
            RBucket<BigInteger> bucket = RedisUtils.getClient().getBucket(TRON_CURRENT_BLOCK_KEY);
            bucket.set(blockNumber);

            // 更新缓存
            lastSavedBlock.set(blockNumber);

            log.info("TRON链扫描进度已手动设置: {}", blockNumber);

        } catch (Exception e) {
            log.error("TRON链手动设置扫描进度失败: blockNumber={}, error={}",
                blockNumber, e.getMessage());
        }
    }

    /**
     * 从Redis获取当前保存的进度
     */
    public BigInteger getProgressFromRedis() {
        try {
            RBucket<BigInteger> bucket = RedisUtils.getClient().getBucket(TRON_CURRENT_BLOCK_KEY);
            if (bucket.isExists()) {
                return bucket.get();
            }
        } catch (Exception e) {
            log.error("从Redis获取TRON链扫描进度失败: {}", e.getMessage());
        }
        return null;
    }
}
