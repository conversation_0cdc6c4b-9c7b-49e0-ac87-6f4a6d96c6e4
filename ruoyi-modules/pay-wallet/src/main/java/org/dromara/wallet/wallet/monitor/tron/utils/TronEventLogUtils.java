package org.dromara.wallet.wallet.monitor.tron.utils;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.utils.TronAddressUtils;
import org.dromara.common.scanning.chain.model.tron.TronLogModel;
import org.dromara.wallet.wallet.monitor.tron.dto.MyTronTransactionModel;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * TRON事件日志工具类
 * 提供事件日志解析、过滤和转换功能
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
public class TronEventLogUtils {

    /**
     * Transfer事件签名哈希
     * Transfer(address indexed from, address indexed to, uint256 value)
     */
    public static final String TRANSFER_EVENT_SIGNATURE = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";

    /**
     * 从getTransactionInfoById的响应中解析事件日志
     *
     * @param transactionInfo API响应的JsonNode
     * @return 事件日志列表
     */
    public static List<TronLogModel> parseEventLogs(JsonNode transactionInfo) {
        return parseEventLogs(transactionInfo, null, null, null);
    }

    /**
     * 从getTransactionInfoById的响应中解析事件日志（增强版本）
     * 🚀 数据完整性修复：提取完整的交易元数据
     *
     * @param transactionInfo API响应的JsonNode
     * @param txId           交易ID
     * @param blockNumber    区块号
     * @param blockTimeStamp 区块时间戳
     * @return 事件日志列表
     */
    public static List<TronLogModel> parseEventLogs(JsonNode transactionInfo, String txId,
                                                   java.math.BigInteger blockNumber, Long blockTimeStamp) {
        List<TronLogModel> logs = new ArrayList<>();

        if (transactionInfo == null || !transactionInfo.has("log")) {
            return logs;
        }

        JsonNode logArray = transactionInfo.get("log");
        if (!logArray.isArray()) {
            return logs;
        }

        // 从API响应中提取交易元数据
        Long apiBlockTimeStamp = extractBlockTimeStamp(transactionInfo);
        java.math.BigDecimal transactionFee = extractTransactionFee(transactionInfo);

        for (JsonNode logNode : logArray) {
            try {
                TronLogModel logModel = parseLogNode(logNode);
                if (logModel != null) {
                    // 🚀 数据完整性修复：设置交易元数据
                    setTransactionMetadata(logModel, txId, blockNumber,
                        blockTimeStamp != null ? blockTimeStamp : apiBlockTimeStamp, transactionFee);
                    logs.add(logModel);
                }
            } catch (Exception e) {
                log.warn("解析事件日志失败: {}", e.getMessage());
            }
        }

        return logs;
    }

    /**
     * 解析单个日志节点
     */
    private static TronLogModel parseLogNode(JsonNode logNode) {
        if (logNode == null) {
            return null;
        }

        TronLogModel logModel = new TronLogModel();

        // 解析合约地址
        if (logNode.has("address")) {
            String address = logNode.get("address").asText();
            log.debug("原始合约地址: {}", address);

            // 如果是hex格式，转换为base58格式
            if (address.startsWith("0x") || address.matches("^[0-9a-fA-F]+$")) {
                try {
                    // 使用新的完整地址转换方法，支持42位TRON地址
                    String convertedAddress = TronAddressUtils.hexToAddressFull(address);
                    log.debug("地址转换成功: {} -> {}", address, convertedAddress);
                    address = convertedAddress;
                } catch (Exception e) {
                    log.warn("地址格式转换失败，使用原始地址: originalAddress={}, error={}", address, e.getMessage());
                    // 转换失败时保持原始地址，但这可能导致匹配失败
                }
            } else {
                log.debug("地址已经是Base58格式: {}", address);
            }
            logModel.setAddress(address);
        }

        // 解析topics
        if (logNode.has("topics")) {
            JsonNode topicsArray = logNode.get("topics");
            if (topicsArray.isArray()) {
                List<String> topics = new ArrayList<>();
                for (JsonNode topic : topicsArray) {
                    String topicStr = topic.asText();
                    if (!topicStr.startsWith("0x")) {
                        topicStr = "0x" + topicStr;
                    }
                    topics.add(topicStr);
                }
                logModel.setTopics(topics);
            }
        }

        // 解析data
        if (logNode.has("data")) {
            String data = logNode.get("data").asText();
            if (!data.startsWith("0x")) {
                data = "0x" + data;
            }
            logModel.setData(data);
        }

        return logModel;
    }

    /**
     * 为交易模型加载事件日志
     *
     * @param transaction     交易模型
     * @param transactionInfo getTransactionInfoById的API响应
     */
    public static void loadEventLogs(MyTronTransactionModel transaction, JsonNode transactionInfo) {
        if (transaction == null || transactionInfo == null) {
            return;
        }

        List<TronLogModel> logs = parseEventLogs(transactionInfo);
        transaction.setLogs(logs);

        // 只在有多个事件日志时记录
        if (logs.size() > 1) {
            log.debug("为交易{}加载了{}个事件日志", transaction.getTxID(), logs.size());
        }
    }

    /**
     * 过滤Transfer事件
     */
    public static List<TronLogModel> filterTransferEvents(List<TronLogModel> logs) {
        if (logs == null) {
            return new ArrayList<>();
        }

        return logs.stream()
            .filter(TronLogModel::isTransferEvent)
            .toList();
    }

    /**
     * 过滤涉及指定地址的Transfer事件
     */
    public static List<TronLogModel> filterTransferEventsForAddress(List<TronLogModel> logs, String address) {
        if (logs == null || address == null) {
            return new ArrayList<>();
        }

        return logs.stream()
            .filter(TronLogModel::isTransferEvent)
            .filter(log -> log.containsAddress(address))
            .toList();
    }

    /**
     * 过滤指定合约的事件
     */
    public static List<TronLogModel> filterEventsByContract(List<TronLogModel> logs, String contractAddress) {
        if (logs == null || contractAddress == null) {
            return new ArrayList<>();
        }

        String normalizedContract = contractAddress.toLowerCase();
        return logs.stream()
            .filter(log -> log.getAddress() != null &&
                log.getAddress().toLowerCase().equals(normalizedContract))
            .toList();
    }

    /**
     * 将Transfer事件的金额转换为可读格式
     *
     * @param transferLog Transfer事件日志
     * @param decimals    代币精度
     * @return 可读格式的金额
     */
    public static BigDecimal parseTransferAmount(TronLogModel transferLog, int decimals) {
        if (transferLog == null || !transferLog.isTransferEvent()) {
            return BigDecimal.ZERO;
        }

        String amountStr = transferLog.getTransferAmount();
        if (amountStr == null || amountStr.equals("0")) {
            return BigDecimal.ZERO;
        }

        try {
            BigInteger rawAmount = new BigInteger(amountStr);
            BigDecimal divisor = BigDecimal.TEN.pow(decimals);
            return new BigDecimal(rawAmount).divide(divisor);
        } catch (Exception e) {
            log.warn("解析Transfer金额失败: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 转换地址格式（hex to base58）
     * 支持完整的TRON十六进制地址格式
     */
    public static String convertAddressFormat(String hexAddress) {
        if (hexAddress == null || hexAddress.isEmpty()) {
            return hexAddress;
        }

        try {
            // 如果已经是base58格式，直接返回
            if (!hexAddress.startsWith("0x") && !hexAddress.matches("^[0-9a-fA-F]+$")) {
                log.debug("地址已经是Base58格式: {}", hexAddress);
                return hexAddress;
            }

            // 使用新的完整地址转换方法
            String convertedAddress = TronAddressUtils.hexToAddressFull(hexAddress);
            log.debug("地址格式转换成功: {} -> {}", hexAddress, convertedAddress);
            return convertedAddress;

        } catch (Exception e) {
            log.warn("地址格式转换失败，返回原始地址: originalAddress={}, error={}", hexAddress, e.getMessage());
            return hexAddress;
        }
    }

    /**
     * 检查交易是否包含指定类型的事件
     */
    public static boolean hasEventWithSignature(MyTronTransactionModel transaction, String eventSignature) {
        if (transaction == null || !transaction.isLogsLoaded() || eventSignature == null) {
            return false;
        }
        List<TronLogModel> logs = transaction.getLogs();
        if (logs == null) {
            return false;
        }

        return logs.stream()
            .anyMatch(log -> eventSignature.equalsIgnoreCase(log.getEventSignature()));
    }

    /**
     * 获取事件日志的摘要信息（用于日志输出）
     */
    public static String getEventLogSummary(MyTronTransactionModel transaction) {
        if (transaction == null || !transaction.isLogsLoaded()) {
            return "事件日志未加载";
        }

        List<TronLogModel> logs = transaction.getLogs();
        if (logs == null || logs.isEmpty()) {
            return "无事件日志";
        }

        long transferCount = logs.stream().filter(TronLogModel::isTransferEvent).count();
        long otherCount = logs.size() - transferCount;

        return String.format("事件日志: %d个Transfer事件, %d个其他事件", transferCount, otherCount);
    }

    /**
     * 从getTransactionInfoById响应中提取区块时间戳
     */
    private static Long extractBlockTimeStamp(JsonNode transactionInfo) {
        try {
            if (transactionInfo.has("blockTimeStamp")) {
                return transactionInfo.get("blockTimeStamp").asLong();
            }
        } catch (Exception e) {
            log.debug("提取区块时间戳失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从getTransactionInfoById响应中提取交易手续费
     * TRON交易手续费 = net_fee + energy_fee
     */
    private static java.math.BigDecimal extractTransactionFee(JsonNode transactionInfo) {
        try {
            java.math.BigDecimal totalFee = java.math.BigDecimal.ZERO;

            // 方法1：从fee字段直接获取
            if (transactionInfo.has("fee")) {
                long fee = transactionInfo.get("fee").asLong();
                return new java.math.BigDecimal(fee).divide(new java.math.BigDecimal("1000000")); // 转换为TRX
            }

            // 方法2：从receipt字段计算
            if (transactionInfo.has("receipt")) {
                JsonNode receipt = transactionInfo.get("receipt");

                // net_fee（带宽费用）
                if (receipt.has("net_fee")) {
                    long netFee = receipt.get("net_fee").asLong();
                    totalFee = totalFee.add(new java.math.BigDecimal(netFee));
                }

                // energy_fee（能量费用）
                if (receipt.has("energy_fee")) {
                    long energyFee = receipt.get("energy_fee").asLong();
                    totalFee = totalFee.add(new java.math.BigDecimal(energyFee));
                }

                // 转换为TRX（TRON的最小单位是sun，1 TRX = 1,000,000 sun）
                return totalFee.divide(new java.math.BigDecimal("1000000"));
            }

        } catch (Exception e) {
            log.debug("提取交易手续费失败: {}", e.getMessage());
        }
        return java.math.BigDecimal.ZERO;
    }

    /**
     * 设置TronLogModel的交易元数据
     * 🚀 数据完整性修复：确保所有必要字段都被正确设置
     */
    private static void setTransactionMetadata(TronLogModel logModel, String txId,
                                             java.math.BigInteger blockNumber, Long blockTimeStamp,
                                             java.math.BigDecimal transactionFee) {
        if (logModel == null) {
            return;
        }

        // 设置交易哈希
        if (txId != null) {
            logModel.setTransactionHash(txId);
        }

        // 设置区块号
        if (blockNumber != null) {
            logModel.setBlockNumber(blockNumber);
        }

        // 设置区块时间戳
        if (blockTimeStamp != null) {
            logModel.setBlockTimeStamp(blockTimeStamp);
        }

        // 设置交易手续费
        if (transactionFee != null) {
            logModel.setTransactionFee(transactionFee);
        }
    }
}
