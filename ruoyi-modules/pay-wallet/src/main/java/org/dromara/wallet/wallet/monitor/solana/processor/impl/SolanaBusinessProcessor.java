package org.dromara.wallet.wallet.monitor.solana.processor.impl;

import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.solana.SolanaWalletConfig;
import org.dromara.wallet.domain.MetaSolanaCstaddressinfo;
import org.dromara.wallet.domain.bo.MetaSolanaTransactionBo;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.dromara.wallet.service.IMetaSolanaCstaddressinfoService;
import org.dromara.wallet.service.IMetaSolanaTransactionsService;
import org.dromara.wallet.wallet.monitor.solana.dto.SolanaTransactionModel;
import org.dromara.wallet.wallet.monitor.solana.processor.SolanaTransactionProcessor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Solana业务处理处理器
 * 
 * <p>功能：</p>
 * <ul>
 *   <li>交易验证（可信度检查、最小金额等）</li>
 *   <li>数据库操作（保存交易记录）</li>
 *   <li>入账处理</li>
 *   <li>TG告警</li>
 *   <li>归集处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaBusinessProcessor implements SolanaTransactionProcessor {

    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;
    private final IMetaSolanaTransactionsService solanaTransactionsService;
    private final SolanaWalletConfig walletConfig;

    @Override
    public boolean process(SolanaTransactionModel transactionModel, String userAddress) {
        try {
            // 获取解析后的交易列表
            List<MetaSolanaTransactionBo> transactionBoList = transactionModel.getParsedTransactions();
            
            if (transactionBoList == null || transactionBoList.isEmpty()) {
                log.debug("没有解析到的交易数据，跳过业务处理");
                return false; // 没有业务数据，中断处理链
            }

            // 处理所有解析到的交易
            for (MetaSolanaTransactionBo transactionBo : transactionBoList) {
                try {
                    processTransaction(transactionBo);
                } catch (Exception e) {
                    log.error("处理交易{}失败: {}", transactionBo.getTxid(), e.getMessage());
                    // 单个交易失败不影响其他交易，继续处理
                }
            }

            log.info("Solana业务处理完成，交易: {}, 处理数量: {}", 
                transactionModel.getTransactionSignature(), transactionBoList.size());
            
            return true; // 业务处理完成

        } catch (Exception e) {
            log.error("Solana业务处理失败: {}", e.getMessage());
            return false; // 业务处理失败，中断处理链
        }
    }

    @Override
    public String getProcessorName() {
        return "SolanaBusinessProcessor";
    }

    @Override
    public int getOrder() {
        return 300; // 第三个执行
    }

    /**
     * 处理单个交易
     *
     * @param transactionBo 交易业务对象
     */
    private void processTransaction(MetaSolanaTransactionBo transactionBo) {
        String transactionType = transactionBo.getType();
        
        if ("receive".equals(transactionType)) {
            processReceiveTransaction(transactionBo);
        } else if ("collect".equals(transactionType) || "send".equals(transactionType)) {
            processSendTransaction(transactionBo);
        } else {
            log.debug("未知交易类型: {}, 跳过处理", transactionType);
        }
    }

    /**
     * 处理接收交易
     *
     * @param transactionBo 交易数据
     */
    private void processReceiveTransaction(MetaSolanaTransactionBo transactionBo) {
        try {
            log.info("Solana链开始处理接收交易: txId={}, address={}, amount={} {}",
                transactionBo.getTxid(), transactionBo.getAddress(),
                transactionBo.getAmount(), getTokenSymbol(transactionBo.getContract()));

            // 获取该地址对应的钱包信息
            MetaSolanaCstaddressinfo oneByAddress = solanaCstaddressinfoService.getOneByAddress(transactionBo.getAddress());
            // 主钱包地址
            MetaMainAddress metaMainAddress = walletConfig.getMainAddress();

            if (!verifyTransactionTrustworthiness(transactionBo, oneByAddress)) {
                // 交易不可信，不入账
                log.info("Solana链接收交易验证失败（可信度）: txId={}", transactionBo.getTxid());
                return;
            }

            // 2. 验证通过，保存交易记录
            solanaTransactionsService.insertByBo(transactionBo);

            // 3. tg告警
            alarm(transactionBo, oneByAddress, metaMainAddress);

            // 4. 入账处理
            SpringUtil.getBean(this.getClass()).doData(transactionBo.getId(), true);

            log.info("Solana链接收交易验证通过并处理完成: txId={}", transactionBo.getTxid());

        } catch (Exception e) {
            log.error("Solana链处理接收交易失败: txId={}, error={}",
                transactionBo.getTxid(), e.getMessage());
            throw e; // 重新抛出异常，保持原有的错误处理逻辑
        }
    }

    /**
     * 处理发送交易（归集）
     *
     * @param transactionBo 交易数据
     */
    private void processSendTransaction(MetaSolanaTransactionBo transactionBo) {
        try {
            log.info("Solana链开始处理归集交易: txId={}, from={}, amount={} {}",
                transactionBo.getTxid(), transactionBo.getFromaddress(),
                transactionBo.getAmount(), getTokenSymbol(transactionBo.getContract()));

            transactionBo.setType("collect");
            transactionBo.setIssync(2);
            solanaTransactionsService.insertByBo(transactionBo);
            
            // 获取该地址对应的钱包信息
            MetaSolanaCstaddressinfo userWallet = solanaCstaddressinfoService.getOneByAddress(transactionBo.getFromaddress());
            // 主钱包地址
            MetaMainAddress metaMainAddress = walletConfig.getMainAddress();
            
            // tg告警
            alarm(transactionBo, userWallet, metaMainAddress);

            log.info("Solana链归集交易处理完成: txId={}", transactionBo.getTxid());

        } catch (Exception e) {
            log.error("Solana链处理归集交易失败: txId={}, error={}",
                transactionBo.getTxid(), e.getMessage());
            throw e;
        }
    }

    /**
     * 验证交易可信度
     * TODO: 从SolanaTransactionManager迁移验证逻辑
     *
     * @param transactionBo 交易业务对象
     * @param walletInfo 钱包信息
     * @return 是否可信
     */
    private boolean verifyTransactionTrustworthiness(MetaSolanaTransactionBo transactionBo, 
                                                   MetaSolanaCstaddressinfo walletInfo) {
        // TODO: 实现可信度验证逻辑
        return true; // 临时返回true
    }

    /**
     * TG告警
     * TODO: 从SolanaTransactionManager迁移告警逻辑
     *
     * @param transactionBo 交易业务对象
     * @param walletInfo 钱包信息
     * @param mainAddress 主钱包地址
     */
    private void alarm(MetaSolanaTransactionBo transactionBo, 
                      MetaSolanaCstaddressinfo walletInfo, 
                      MetaMainAddress mainAddress) {
        // TODO: 实现告警逻辑
        log.debug("发送TG告警: {}", transactionBo.getTxid());
    }

    /**
     * 入账处理
     * TODO: 从SolanaTransactionManager迁移入账逻辑
     *
     * @param transactionId 交易ID
     * @param needCheckAmount 是否需要检查金额
     */
    public void doData(Long transactionId, boolean needCheckAmount) {
        // TODO: 实现入账逻辑
        log.debug("处理入账: transactionId={}, needCheckAmount={}", transactionId, needCheckAmount);
    }

    /**
     * 获取代币符号
     *
     * @param contractAddress 合约地址
     * @return 代币符号
     */
    private String getTokenSymbol(String contractAddress) {
        // TODO: 实现代币符号获取逻辑
        return "SOL"; // 临时返回SOL
    }
}
