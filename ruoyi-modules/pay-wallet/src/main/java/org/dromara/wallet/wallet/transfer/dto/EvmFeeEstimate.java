package org.dromara.wallet.wallet.transfer.dto;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * EVM手续费估算实现
 * 
 * <p>封装EVM链转账的手续费估算信息，包括：</p>
 * <ul>
 *   <li>Gas Price（单位：wei）</li>
 *   <li>Gas Limit</li>
 *   <li>等价原生代币需求量（ETH、BNB等）</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public record EvmFeeEstimate(
    BigInteger gasPrice,
    BigInteger gasLimit,
    BigDecimal nativeTokenNeeded,
    String nativeTokenSymbol
) implements FeeEstimate {

    @Override
    public BigDecimal getNativeTokenNeeded() {
        return nativeTokenNeeded;
    }

    @Override
    public String getNativeTokenSymbol() {
        return nativeTokenSymbol;
    }

    @Override
    public String getDescription() {
        return String.format("Gas Price: %s wei, Gas Limit: %s, %s: %s", 
            gasPrice, gasLimit, nativeTokenSymbol, nativeTokenNeeded);
    }

    /**
     * 创建原生代币转账的手续费估算
     */
    public static EvmFeeEstimate forNativeTransfer(BigInteger gasPrice, BigInteger gasLimit, 
                                                  BigDecimal nativeTokenNeeded, String nativeTokenSymbol) {
        return new EvmFeeEstimate(gasPrice, gasLimit, nativeTokenNeeded, nativeTokenSymbol);
    }

    /**
     * 创建ERC20代币转账的手续费估算
     */
    public static EvmFeeEstimate forTokenTransfer(BigInteger gasPrice, BigInteger gasLimit, 
                                                 BigDecimal nativeTokenNeeded, String nativeTokenSymbol) {
        return new EvmFeeEstimate(gasPrice, gasLimit, nativeTokenNeeded, nativeTokenSymbol);
    }

    /**
     * 计算总手续费（Gas Price * Gas Limit）
     */
    public BigInteger getTotalGasCost() {
        return gasPrice.multiply(gasLimit);
    }
}
