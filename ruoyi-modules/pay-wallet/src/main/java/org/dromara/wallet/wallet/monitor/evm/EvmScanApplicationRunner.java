package org.dromara.wallet.wallet.monitor.evm;

import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedissonLockExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.biz.thread.EventThreadPool;
import org.dromara.wallet.config.BlockchainType;
import org.dromara.wallet.config.facade.ChainConfigFacadeManager;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.dromara.wallet.service.WalletScanService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Set;

/**
 * EVM链扫描自动启动器
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>应用启动时自动检查配置并启动所有启用的EVM链扫描任务</li>
 *   <li>支持配置开关控制是否自动启动</li>
 *   <li>使用分布式锁确保集群环境下只有一个节点启动扫描</li>
 *   <li>自动从Redis恢复扫描进度</li>
 *   <li>完整的错误处理和日志记录</li>
 * </ul>
 *
 * <p>支持的EVM链：</p>
 * <ul>
 *   <li>BSC (Binance Smart Chain)</li>
 *   <li>ARB (Arbitrum One)</li>
 *   <li>BASE (Base)</li>
 * </ul>
 *
 * <p>配置要求：</p>
 * <ul>
 *   <li>{chain}.monitor.auto-scan-enabled=true 启用自动扫描</li>
 *   <li>{chain}.wallet.enabled=true 启用钱包功能</li>
 *   <li>{chain}.rpc.enabled=true 启用RPC</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EvmScanApplicationRunner implements ApplicationRunner {

    private final ChainConfigFacadeManager chainConfigManager;
    private final WalletScanService scanService;
    private final LockTemplate lockTemplate;

    /**
     * 分布式锁相关常量
     */
    private static final String SCAN_LOCK_KEY_PREFIX = "evm:scan:auto_start:";
    private static final long LOCK_WAIT_TIME = 5000L; // 等待锁的时间：5秒
    private static final long LOCK_LEASE_TIME = 60000L; // 锁的持有时间：60秒

    /**
     * 应用启动时执行自动扫描启动逻辑
     * 使用分布式锁确保集群环境下只有一个节点启动扫描任务
     *
     * @param args 应用启动参数
     */
    @Override
    public void run(ApplicationArguments args) {
        try {
            log.info("EVM链扫描自动启动器开始检查配置...");

            // {{ AURA-X: Remove - 移除重复的EventThreadPool初始化，统一由WalletScanService管理. Approval: 寸止(ID:1678886405). }}
            // EventThreadPool已由WalletScanService统一初始化，无需重复调用

            // 获取所有EVM兼容链的名称
            Set<String> evmChainNames = BlockchainType.getEvmCompatibleChainNames();
            log.info("检测到EVM兼容链: {}", evmChainNames);

            // 为每个EVM链尝试启动扫描任务
            for (String chainName : evmChainNames) {
                startChainScan(chainName);
            }

            log.info("EVM链扫描自动启动器完成所有链的检查");

        } catch (Exception e) {
            log.error("EVM链扫描自动启动过程中发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 为指定链启动扫描任务
     *
     * @param chainName 链名称
     */
    private void startChainScan(String chainName) {
        try {
            log.info("开始检查{}链的自动扫描配置...", chainName);

            // 1. 规范化链名称（将别名转换为主链名称）
            String normalizedChainName = BlockchainType.normalizeChainName(chainName);
            if (normalizedChainName == null) {
                log.warn("不支持的链名称: {}，跳过自动扫描启动", chainName);
                return;
            }

            // 如果链名称发生了规范化，记录日志
            if (!chainName.equals(normalizedChainName)) {
                log.info("链名称已规范化: {} -> {}", chainName, normalizedChainName);
            }

            // 2. 获取链配置门面
            EvmConfigFacade configFacade = chainConfigManager.getEvmConfigFacade(normalizedChainName);
            if (configFacade == null) {
                log.warn("{}链配置门面不存在，跳过自动扫描启动", normalizedChainName);
                return;
            }

            // 3. 检查基础配置是否启用
            if (!configFacade.isEnabled()) {
                log.info("{}链配置未启用，跳过自动扫描启动", normalizedChainName);
                return;
            }

            // 4. 检查自动扫描配置是否启用
            if (!configFacade.isAutoScanEnabled()) {
                log.info("{}链自动扫描功能已禁用，跳过自动启动", normalizedChainName);
                return;
            }

            // 5. 检查扫描服务是否已在运行（使用规范化名称）
            if (scanService.isChainScanning(normalizedChainName)) {
                log.warn("{}链扫描任务已在运行中，跳过自动启动", normalizedChainName);
                return;
            }

            // 6. 尝试获取分布式锁（使用规范化名称作为锁键）
            String lockKey = SCAN_LOCK_KEY_PREFIX + normalizedChainName.toLowerCase();
            log.info("尝试获取{}链扫描启动分布式锁: {}", normalizedChainName, lockKey);

            LockInfo lockInfo = lockTemplate.lock(lockKey, LOCK_WAIT_TIME, LOCK_LEASE_TIME, RedissonLockExecutor.class);

            if (lockInfo == null) {
                log.info("未能获取{}链扫描启动锁，可能其他节点已启动扫描任务", normalizedChainName);
                return;
            }

            try {
                log.info("成功获取{}链扫描启动锁，开始启动扫描任务", normalizedChainName);

                // 7. 再次检查扫描服务状态（双重检查，使用规范化名称）
                if (scanService.isChainScanning(normalizedChainName)) {
                    log.warn("获取锁后发现{}链扫描任务已在运行中，跳过启动", normalizedChainName);
                    return;
                }

                // 8. 获取自动扫描配置参数
                long startBlockLong = configFacade.getAutoScanStartBlock();
                long scanPeriod = configFacade.getAutoScanPeriod();

                // 9. 转换起始区块号
                BigInteger startBlock = BigInteger.valueOf(Math.max(0, startBlockLong));

                log.info("开始自动启动{}链扫描任务，起始区块: {}, 扫描周期: {}ms", normalizedChainName, startBlock, scanPeriod);

                // 10. 启动扫描任务
                boolean success = scanService.startEvmChainScan(configFacade, startBlock, scanPeriod, true);

                if (success) {
                    log.info("{}链扫描任务自动启动成功，当前节点成为主扫描节点", normalizedChainName);
                } else {
                    log.error("{}链扫描任务自动启动失败", normalizedChainName);
                }

            } finally {
                // 11. 释放分布式锁
                lockTemplate.releaseLock(lockInfo);
                log.info("已释放{}链扫描启动分布式锁", normalizedChainName);
            }

        } catch (Exception e) {
            log.error("{}链扫描自动启动过程中发生异常: {}", chainName, e.getMessage(), e);
        }
    }
}
