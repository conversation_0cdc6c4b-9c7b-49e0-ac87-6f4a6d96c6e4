package org.dromara.wallet.wallet.monitor.solana.processor;

import org.dromara.wallet.wallet.monitor.solana.dto.SolanaTransactionModel;

/**
 * Solana交易处理器接口
 * 
 * <p>设计原则：</p>
 * <ul>
 *   <li>每个处理器只返回boolean，表示是否继续处理</li>
 *   <li>处理器按顺序执行，任何一个返回false都会中断链条</li>
 *   <li>处理器内部完成自己的业务逻辑</li>
 *   <li>通过SolanaTransactionModel在处理器间传递数据</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface SolanaTransactionProcessor {

    /**
     * 处理交易
     *
     * @param transactionModel Solana交易模型
     * @param userAddress 用户指定的关联地址（可为null）
     * @return true-继续处理链条，false-中断处理链条
     */
    boolean process(SolanaTransactionModel transactionModel, String userAddress);

    /**
     * 获取处理器名称（用于日志）
     *
     * @return 处理器名称
     */
    String getProcessorName();

    /**
     * 获取处理器执行顺序
     * 数字越小越先执行
     *
     * @return 执行顺序
     */
    int getOrder();
}
