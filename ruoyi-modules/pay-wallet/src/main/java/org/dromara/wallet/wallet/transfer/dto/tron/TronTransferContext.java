package org.dromara.wallet.wallet.transfer.dto.tron;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * TRON转账上下文
 * 
 * <p>封装TRON转账过程中的上下文信息，用于在转账流程的各个步骤之间传递数据</p>
 * <p>包含转账参数、中间结果、性能统计等信息</p>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public class TronTransferContext {
    
    // ==================== 基础转账信息 ====================
    
    private final String fromAddress;
    private final String toAddress;
    private final String privateKey;
    private final BigDecimal amount;
    private final String tokenSymbol;
    private final boolean isNativeToken;
    
    // ==================== 转账流程状态 ====================
    
    private TronFeeEstimate feeEstimate;
    private FeeProvisionResult feeProvisionResult;
    private String transactionHash;
    private boolean confirmed;
    
    // ==================== 性能统计 ====================
    
    private final LocalDateTime startTime;
    private LocalDateTime feeEstimateTime;
    private LocalDateTime feeProvisionTime;
    private LocalDateTime transactionTime;
    private LocalDateTime confirmationTime;
    
    /**
     * 构造TRON转账上下文
     * 
     * @param fromAddress   发送方地址
     * @param toAddress     接收方地址
     * @param privateKey    发送方私钥
     * @param amount        转账金额
     * @param tokenSymbol   代币符号
     * @param isNativeToken 是否为原生代币
     */
    public TronTransferContext(String fromAddress, String toAddress, String privateKey,
                             BigDecimal amount, String tokenSymbol, boolean isNativeToken) {
        this.fromAddress = fromAddress;
        this.toAddress = toAddress;
        this.privateKey = privateKey;
        this.amount = amount;
        this.tokenSymbol = tokenSymbol;
        this.isNativeToken = isNativeToken;
        this.startTime = LocalDateTime.now();
    }
    
    // ==================== Getters ====================
    
    public String getFromAddress() { return fromAddress; }
    public String getToAddress() { return toAddress; }
    public String getPrivateKey() { return privateKey; }
    public BigDecimal getAmount() { return amount; }
    public String getTokenSymbol() { return tokenSymbol; }
    public boolean isNativeToken() { return isNativeToken; }
    
    public TronFeeEstimate getFeeEstimate() { return feeEstimate; }
    public FeeProvisionResult getFeeProvisionResult() { return feeProvisionResult; }
    public String getTransactionHash() { return transactionHash; }
    public boolean isConfirmed() { return confirmed; }
    
    public LocalDateTime getStartTime() { return startTime; }
    public LocalDateTime getFeeEstimateTime() { return feeEstimateTime; }
    public LocalDateTime getFeeProvisionTime() { return feeProvisionTime; }
    public LocalDateTime getTransactionTime() { return transactionTime; }
    public LocalDateTime getConfirmationTime() { return confirmationTime; }
    
    // ==================== 状态更新方法 ====================
    
    /**
     * 设置手续费预估结果
     */
    public void setFeeEstimate(TronFeeEstimate feeEstimate) {
        this.feeEstimate = feeEstimate;
        this.feeEstimateTime = LocalDateTime.now();
    }
    
    /**
     * 设置手续费提供结果
     */
    public void setFeeProvisionResult(FeeProvisionResult feeProvisionResult) {
        this.feeProvisionResult = feeProvisionResult;
        this.feeProvisionTime = LocalDateTime.now();
    }
    
    /**
     * 设置交易哈希
     */
    public void setTransactionHash(String transactionHash) {
        this.transactionHash = transactionHash;
        this.transactionTime = LocalDateTime.now();
    }
    
    /**
     * 标记交易已确认
     */
    public void markConfirmed() {
        this.confirmed = true;
        this.confirmationTime = LocalDateTime.now();
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 获取总执行时间（毫秒）
     */
    public long getTotalExecutionTimeMs() {
        LocalDateTime endTime = confirmationTime != null ? confirmationTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, endTime).toMillis();
    }
    
    /**
     * 获取转账摘要
     */
    public String getSummary() {
        return String.format("TRON转账: %s %s -> %s, 金额: %s %s, 状态: %s", 
            fromAddress.substring(0, 8) + "...", 
            toAddress.substring(0, 8) + "...",
            toAddress.substring(toAddress.length() - 8),
            amount, tokenSymbol,
            confirmed ? "已确认" : (transactionHash != null ? "已提交" : "处理中"));
    }
    
    /**
     * 检查是否已完成转账
     */
    public boolean isCompleted() {
        return transactionHash != null && confirmed;
    }
    
    /**
     * 检查是否使用了手续费钱包
     */
    public boolean isFeeWalletUsed() {
        return feeProvisionResult != null && feeProvisionResult.isProvisionUsed();
    }
}
