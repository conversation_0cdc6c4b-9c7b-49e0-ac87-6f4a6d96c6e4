package org.dromara.wallet.wallet.transfer.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 统一手续费提供结果
 * 
 * <p>封装手续费提供操作的结果信息，包括：</p>
 * <ul>
 *   <li>是否成功提供手续费</li>
 *   <li>提供的手续费数量</li>
 *   <li>提供方式（手续费钱包、第三方API等）</li>
 *   <li>提供的原生代币符号</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Data
@Builder
public class FeeProvisionResult {

    /**
     * 是否成功提供手续费
     */
    private boolean provided;

    /**
     * 提供的手续费数量
     */
    private BigDecimal amount;

    /**
     * 提供的原生代币符号
     */
    private String tokenSymbol;

    /**
     * 手续费提供方式
     */
    private ProvisionMethod method;

    /**
     * 提供失败时的错误信息
     */
    private String errorMessage;

    /**
     * 手续费提供方式枚举
     */
    public enum ProvisionMethod {
        /**
         * 无需提供手续费（用户余额充足）
         */
        NONE("无需提供"),
        
        /**
         * 通过手续费钱包提供
         */
        FEE_WALLET("手续费钱包"),
        
        /**
         * 通过第三方API提供
         */
        THIRD_PARTY_API("第三方API"),
        
        /**
         * 通过原生代币转账提供（如TRX转账）
         */
        NATIVE_TRANSFER("原生代币转账");

        private final String description;

        ProvisionMethod(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建无需提供手续费的结果
     */
    public static FeeProvisionResult noProvision() {
        return FeeProvisionResult.builder()
            .provided(false)
            .amount(BigDecimal.ZERO)
            .method(ProvisionMethod.NONE)
            .build();
    }

    /**
     * 创建手续费钱包提供成功的结果
     */
    public static FeeProvisionResult feeWalletSuccess(BigDecimal amount, String tokenSymbol) {
        return FeeProvisionResult.builder()
            .provided(true)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .method(ProvisionMethod.FEE_WALLET)
            .build();
    }

    /**
     * 创建第三方API提供成功的结果
     */
    public static FeeProvisionResult thirdPartyApiSuccess(BigDecimal amount, String tokenSymbol) {
        return FeeProvisionResult.builder()
            .provided(true)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .method(ProvisionMethod.THIRD_PARTY_API)
            .build();
    }

    /**
     * 创建原生代币转账提供成功的结果
     */
    public static FeeProvisionResult nativeTransferSuccess(BigDecimal amount, String tokenSymbol) {
        return FeeProvisionResult.builder()
            .provided(true)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .method(ProvisionMethod.NATIVE_TRANSFER)
            .build();
    }

    /**
     * 创建提供失败的结果
     */
    public static FeeProvisionResult failure(String errorMessage) {
        return FeeProvisionResult.builder()
            .provided(false)
            .amount(BigDecimal.ZERO)
            .method(ProvisionMethod.NONE)
            .errorMessage(errorMessage)
            .build();
    }

    // ==================== 便捷方法 ====================

    /**
     * 获取提供方式的描述
     */
    public String getMethodDescription() {
        return method != null ? method.getDescription() : "未知";
    }

    /**
     * 是否通过外部方式提供（非用户自有余额）
     */
    public boolean isExternalProvision() {
        return provided && method != ProvisionMethod.NONE;
    }

    /**
     * 获取结果描述
     */
    public String getDescription() {
        if (!provided) {
            return method == ProvisionMethod.NONE ? "用户余额充足，无需提供手续费" : 
                   "手续费提供失败: " + (errorMessage != null ? errorMessage : "未知错误");
        }
        return String.format("通过%s提供 %s %s", getMethodDescription(), amount, tokenSymbol);
    }
}
