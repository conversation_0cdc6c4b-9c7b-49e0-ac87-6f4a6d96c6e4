package org.dromara.wallet.wallet.transfer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.wallet.wallet.transfer.enums.TransactionStatus;

import java.time.LocalDateTime;

/**
 * 交易确认结果
 *
 * <p>封装交易确认过程的结果信息，包括：</p>
 * <ul>
 *   <li>确认状态和结果</li>
 *   <li>确认耗时和重试信息</li>
 *   <li>错误信息和诊断数据</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionConfirmationResult {

    /**
     * 交易哈希
     */
    private String txHash;

    /**
     * 交易状态
     */
    private TransactionStatus status;

    /**
     * 是否确认成功
     */
    private boolean confirmed;

    /**
     * 确认开始时间
     */
    @Builder.Default
    private LocalDateTime startTime = LocalDateTime.now();

    /**
     * 确认结束时间
     */
    private LocalDateTime endTime;

    /**
     * 确认耗时（毫秒）
     */
    private Long confirmationTimeMs;

    /**
     * 实际确认数
     * 交易获得的实际确认区块数
     */
    private Integer actualConfirmations;

    /**
     * 重试次数
     * 确认过程中的重试次数
     */
    private int retryCount;

    /**
     * 错误信息
     * 确认失败时的错误描述
     */
    private String errorMessage;

    /**
     * 区块高度
     * 交易被包含的区块高度
     */
    private Long blockHeight;

    /**
     * 区块哈希
     * 交易被包含的区块哈希
     */
    private String blockHash;

    /**
     * 额外信息
     * 链特定的额外确认信息
     */
    private String extraInfo;

    // ==================== 静态工厂方法 ====================

    /**
     * 创建确认成功结果
     */
    public static TransactionConfirmationResult success(String txHash, int confirmations) {
        TransactionConfirmationResult result = TransactionConfirmationResult.builder()
            .txHash(txHash)
            .status(TransactionStatus.CONFIRMED)
            .confirmed(true)
            .actualConfirmations(confirmations)
            .endTime(LocalDateTime.now())
            .build();
        // 确保计算确认耗时
        result.calculateConfirmationTime();
        return result;
    }

    /**
     * 创建确认成功结果（包含区块信息）
     */
    public static TransactionConfirmationResult successWithBlock(String txHash, int confirmations,
                                                               Long blockHeight, String blockHash) {
        TransactionConfirmationResult result = TransactionConfirmationResult.builder()
            .txHash(txHash)
            .status(TransactionStatus.CONFIRMED)
            .confirmed(true)
            .actualConfirmations(confirmations)
            .blockHeight(blockHeight)
            .blockHash(blockHash)
            .endTime(LocalDateTime.now())
            .build();
        // 确保计算确认耗时
        result.calculateConfirmationTime();
        return result;
    }

    /**
     * 创建确认失败结果
     */
    public static TransactionConfirmationResult failure(String txHash, TransactionStatus status, String errorMessage) {
        TransactionConfirmationResult result = TransactionConfirmationResult.builder()
            .txHash(txHash)
            .status(status)
            .confirmed(false)
            .errorMessage(errorMessage)
            .endTime(LocalDateTime.now())
            .build();
        // 确保计算确认耗时
        result.calculateConfirmationTime();
        return result;
    }

    /**
     * 创建超时结果
     */
    public static TransactionConfirmationResult timeout(String txHash, String errorMessage) {
        return failure(txHash, TransactionStatus.TIMEOUT, errorMessage);
    }

    /**
     * 创建跳过确认结果
     */
    public static TransactionConfirmationResult skipped(String txHash, String reason) {
        TransactionConfirmationResult result = TransactionConfirmationResult.builder()
            .txHash(txHash)
            .status(TransactionStatus.PENDING)
            .confirmed(true) // 跳过确认视为成功
            .extraInfo("跳过确认: " + reason)
            .endTime(LocalDateTime.now())
            .build();
        // 确保计算确认耗时
        result.calculateConfirmationTime();
        return result;
    }

    /**
     * 创建异步确认待处理结果
     * 用于异步确认模式，表示确认事件已发布但尚未完成
     */
    public static TransactionConfirmationResult asyncPending(String txHash, String requestId, String message) {
        TransactionConfirmationResult result = TransactionConfirmationResult.builder()
            .txHash(txHash)
            .status(TransactionStatus.PENDING)
            .confirmed(false)
            .extraInfo(String.format("异步确认中 (requestId: %s): %s", requestId, message))
            .startTime(LocalDateTime.now())
            .build();
        // 确保计算确认耗时
        result.calculateConfirmationTime();
        return result;
    }

    // ==================== 便捷方法 ====================

    /**
     * 计算确认耗时
     */
    public void calculateConfirmationTime() {
        if (startTime != null && endTime != null) {
            confirmationTimeMs = java.time.Duration.between(startTime, endTime).toMillis();
        } else if (confirmationTimeMs == null) {
            // 如果时间信息不完整，设置为0避免空指针异常
            confirmationTimeMs = 0L;
        }
    }

    /**
     * 标记确认完成
     */
    public void markCompleted() {
        if (endTime == null) {
            endTime = LocalDateTime.now();
            calculateConfirmationTime();
        }
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        retryCount++;
    }

    /**
     * 获取确认结果描述
     */
    public String getDescription() {
        if (confirmed) {
            if (status == TransactionStatus.PENDING) {
                return "跳过确认";
            }
            return String.format("确认成功 (确认数: %d, 耗时: %dms)",
                actualConfirmations != null ? actualConfirmations : 0,
                confirmationTimeMs != null ? confirmationTimeMs : 0);
        } else {
            return String.format("确认失败: %s (重试: %d次, 耗时: %dms)",
                errorMessage != null ? errorMessage : status.getDescription(),
                retryCount,
                confirmationTimeMs != null ? confirmationTimeMs : 0);
        }
    }

    /**
     * 判断是否应该记录为警告
     */
    public boolean shouldLogAsWarning() {
        return !confirmed || retryCount > 0 ||
               (confirmationTimeMs != null && confirmationTimeMs > 30000); // 超过30秒
    }

    /**
     * 判断是否应该记录为错误
     */
    public boolean shouldLogAsError() {
        return !confirmed && status != null && status.isFailure();
    }

    // ==================== 兼容性方法 ====================

    /**
     * 判断确认是否成功
     * 兼容性方法，等同于isConfirmed()
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return confirmed;
    }

    /**
     * 获取确认数
     * 兼容性方法，等同于getActualConfirmations()
     *
     * @return 确认数
     */
    public int getConfirmations() {
        return actualConfirmations != null ? actualConfirmations : 0;
    }

    /**
     * 获取区块号
     * 兼容性方法，等同于getBlockHeight()
     *
     * @return 区块号
     */
    public Long getBlockNumber() {
        return blockHeight;
    }

    /**
     * 获取区块哈希
     * 兼容性方法，等同于getBlockHash()
     *
     * @return 区块哈希
     */
    public String getBlockHash() {
        return blockHash;
    }
}
