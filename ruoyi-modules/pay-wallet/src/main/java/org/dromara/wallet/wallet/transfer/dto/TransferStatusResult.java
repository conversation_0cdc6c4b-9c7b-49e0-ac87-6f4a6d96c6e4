package org.dromara.wallet.wallet.transfer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 转账状态查询结果模型
 *
 * <p>用于封装转账状态查询的结果信息，提供以下数据：</p>
 * <ul>
 *   <li>转账订单的当前状态</li>
 *   <li>交易哈希和确认信息</li>
 *   <li>处理进度和时间信息</li>
 *   <li>错误信息（如果处理失败）</li>
 * </ul>
 *
 * <p>状态说明：</p>
 * <ul>
 *   <li>0: 待处理 - 订单已创建，等待异步处理</li>
 *   <li>1: 处理中 - 异步转账正在执行</li>
 *   <li>2: 已完成 - 转账成功并确认</li>
 *   <li>3: 处理失败 - 转账或确认失败</li>
 *   <li>4: 超时 - 处理超时</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferStatusResult {

    /**
     * 转账订单ID
     */
    private Long orderId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 区块链名称
     */
    private String chainName;

    /**
     * 转账类型
     */
    private String transferType;

    /**
     * 当前状态
     * 0-待处理, 1-处理中, 2-已完成, 3-处理失败, 4-超时
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 交易哈希
     * 转账成功后才有值
     */
    private String txHash;

    /**
     * 所需确认数
     */
    private Integer requiredConfirmations;

    /**
     * 实际确认数
     */
    private Integer actualConfirmations;

    /**
     * 交易所在区块高度
     */
    private Long blockHeight;

    /**
     * 区块哈希
     */
    private String blockHash;

    /**
     * 订单创建时间
     */
    private LocalDateTime createTime;

    /**
     * 处理开始时间
     */
    private LocalDateTime startTime;

    /**
     * 处理结束时间
     */
    private LocalDateTime endTime;

    /**
     * 处理耗时（毫秒）
     */
    private Long processingTimeMs;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 备注信息
     */
    private String remark;

    // ==================== 静态工厂方法 ====================

    /**
     * 创建未找到结果
     */
    public static TransferStatusResult notFound(Long orderId) {
        return TransferStatusResult.builder()
            .orderId(orderId)
            .status(-1)
            .statusDescription("订单不存在")
            .errorMessage("未找到指定的转账订单")
            .build();
    }

    /**
     * 从转账记录BO创建状态结果
     */
    public static TransferStatusResult fromTransferRecord(org.dromara.wallet.domain.bo.MetaTransferRecBo record) {
        if (record == null) {
            return null;
        }

        return TransferStatusResult.builder()
            .orderId(record.getId())
            .requestId(record.getRequestId())
            .chainName(record.getChainName())
            .transferType(record.getTransferType())
            .status(record.getConfirmationStatus())
            .statusDescription(getStatusDescription(record.getConfirmationStatus()))
            .txHash(record.getTransactionHash())
            .requiredConfirmations(record.getRequiredConfirmations())
            .actualConfirmations(record.getActualConfirmations())
            .blockHeight(record.getBlockHeight())
            .blockHash(record.getBlockHash())
            .createTime(record.getCreateTime() != null ?
                record.getCreateTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null)
            .startTime(record.getStartTime() != null ?
                record.getStartTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null)
            .endTime(record.getEndTime() != null ?
                record.getEndTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null)
            .processingTimeMs(record.getConfirmationTimeMs())
            .retryCount(record.getRetryCount())
            .maxRetries(record.getMaxRetries())
            .errorMessage(record.getErrorMessage())
            .remark(record.getRemark())
            .build();
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取状态描述
     */
    private static String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知状态";
        }

        return switch (status) {
            case 0 -> "待处理";
            case 1 -> "处理中";
            case 2 -> "已完成";
            case 3 -> "处理失败";
            case 4 -> "超时";
            default -> "未知状态";
        };
    }

    /**
     * 检查是否处理完成
     */
    public boolean isCompleted() {
        return status != null && (status == 2 || status == 3 || status == 4);
    }

    /**
     * 检查是否处理成功
     */
    public boolean isSuccess() {
        return status != null && status == 2;
    }

    /**
     * 检查是否处理失败
     */
    public boolean isFailed() {
        return status != null && (status == 3 || status == 4);
    }

    /**
     * 检查是否正在处理
     */
    public boolean isProcessing() {
        return status != null && (status == 0 || status == 1);
    }

    /**
     * 获取处理进度百分比
     */
    public Integer getProgressPercentage() {
        if (status == null) {
            return 0;
        }

        return switch (status) {
            case 0 -> 10;  // 待处理
            case 1 -> 50;  // 处理中
            case 2 -> 100; // 已完成
            case 3, 4 -> 100; // 失败也算完成
            default -> 0;
        };
    }

    /**
     * 获取格式化的状态信息
     */
    public String getFormattedStatus() {
        StringBuilder sb = new StringBuilder();
        sb.append(statusDescription);

        if (txHash != null && !txHash.isEmpty()) {
            sb.append(" (TxHash: ").append(txHash.substring(0, Math.min(10, txHash.length()))).append("...)");
        }

        if (actualConfirmations != null && requiredConfirmations != null) {
            sb.append(" [确认: ").append(actualConfirmations).append("/").append(requiredConfirmations).append("]");
        }

        return sb.toString();
    }
}
