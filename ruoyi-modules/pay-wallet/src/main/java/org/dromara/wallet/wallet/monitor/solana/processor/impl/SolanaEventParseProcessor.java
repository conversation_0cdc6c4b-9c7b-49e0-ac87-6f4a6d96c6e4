package org.dromara.wallet.wallet.monitor.solana.processor.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.SolanaCoinType;
import org.dromara.wallet.domain.bo.MetaSolanaTransactionBo;
import org.dromara.wallet.service.IMetaSolanaCstaddressinfoService;
import org.dromara.wallet.wallet.helper.SolanaHelper;
import org.dromara.wallet.wallet.monitor.solana.dto.SolanaTransactionModel;
import org.dromara.wallet.wallet.monitor.solana.processor.SolanaTransactionProcessor;
import org.p2p.solanaj.rpc.types.ConfirmedTransaction;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Solana事件解析处理器
 * 
 * <p>功能：</p>
 * <ul>
 *   <li>解析Solana交易的余额变化</li>
 *   <li>过滤出有效的转账事件</li>
 *   <li>分类交易类型（receive/collect）</li>
 *   <li>生成标准化的交易业务对象</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaEventParseProcessor implements SolanaTransactionProcessor {

    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;
    private final SolanaHelper solanaHelper;

    /**
     * 解析后的交易业务对象存储键名
     */
    public static final String PARSED_TRANSACTIONS_KEY = "solana.parsedTransactions";

    @Override
    public boolean process(SolanaTransactionModel transactionModel, String userAddress) {
        try {
            log.debug("开始解析Solana交易: {}", transactionModel.getTransactionSignature());

            // 解析交易
            List<MetaSolanaTransactionBo> transactionBoList = 
                parseTransactionByBalanceChanges(transactionModel, userAddress);

            if (transactionBoList.isEmpty()) {
                log.debug("交易{}中没有找到有效转账", transactionModel.getTransactionSignature());
                return false; // 没有有效交易，中断处理链
            }

            // 分类和标记交易类型
            classifyTransactions(transactionBoList, userAddress);

            // 将解析结果存储到模型中供后续处理器使用
            transactionModel.setParsedTransactions(transactionBoList);

            log.debug("交易{}解析完成，找到{}个有效转账", 
                transactionModel.getTransactionSignature(), transactionBoList.size());

            return true; // 继续处理

        } catch (Exception e) {
            log.error("Solana交易解析失败: {}", e.getMessage());
            return false; // 解析失败，中断处理链
        }
    }

    @Override
    public String getProcessorName() {
        return "SolanaEventParseProcessor";
    }

    @Override
    public int getOrder() {
        return 200; // 第二个执行
    }

    /**
     * 根据余额变化解析交易的具体实现
     * 迁移自SolanaTransactionManager.parseTransactionByBalanceChanges方法
     */
    private List<MetaSolanaTransactionBo> parseTransactionByBalanceChanges(
            SolanaTransactionModel solTransactionModel, String address) {
        
        List<MetaSolanaTransactionBo> resultList = new ArrayList<>();
        ConfirmedTransaction transaction = solTransactionModel.getTransactionObject();
        boolean isTrustworthy = true;

        // 获取正常账户列表
        List<String> accountKeys = transaction.getTransaction().getMessage().getAccountKeys();
        List<ConfirmedTransaction.TokenBalance> preBalances = transaction.getMeta().getPreTokenBalances();
        List<ConfirmedTransaction.TokenBalance> postBalances = transaction.getMeta().getPostTokenBalances();

        // 监控的地址列表
        HashSet<String> monitoredAddresses = solanaCstaddressinfoService.queryAllAddressSet();
        if (address != null) {
            monitoredAddresses.add(address);
        }
        
        // 监控的代币列表
        Set<String> mintAddresses = Arrays.stream(SolanaCoinType.values())
            .map(SolanaCoinType::getContractAddress)
            .filter(Objects::nonNull) // 过滤null值
            .collect(Collectors.toSet());

        log.debug("监控的代币mint地址: {}", mintAddresses);
        if (mintAddresses.isEmpty()) {
            log.warn("未找到任何有效的代币mint地址，请检查solanacontract.contracts配置");
        }

        // 记录所有代币余额变化
        Map<String, Map<String, BigDecimal>> addressChanges = new HashMap<>();

        // 1. 计算所有账户的代币余额变化
        for (ConfirmedTransaction.TokenBalance post : postBalances) {
            String mint = post.getMint();

            // 如果指定了目标代币且当前代币不是目标代币，则跳过
            if (!mintAddresses.isEmpty() && !mintAddresses.contains(mint)) {
                continue;
            }

            // 增加边界检查，防止索引越界
            int accountIndex = post.getAccountIndex().intValue();
            // 跳过无效的账户索引
            if (accountIndex < 0 || accountIndex >= accountKeys.size()) {
                log.debug("账户索引越界: {} (数组大小: {})", accountIndex, accountKeys.size());
                continue;
            }

            String accountAddress = accountKeys.get(accountIndex);
            BigDecimal postAmount = new BigDecimal(post.getUiTokenAmount().getAmount());
            BigDecimal preAmount;

            // 找对应的交易后余额
            ConfirmedTransaction.TokenBalance pre = findMatchingPreBalance(post, preBalances);
            if (pre == null) {
                preAmount = new BigDecimal(0);
            } else {
                preAmount = new BigDecimal(pre.getUiTokenAmount().getAmount());
            }
            BigDecimal change = postAmount.subtract(preAmount);

            // 记录变化
            if (change.compareTo(BigDecimal.ZERO) != 0) {
                Map<String, BigDecimal> mintChanges = addressChanges.computeIfAbsent(accountAddress, k -> new HashMap<>());
                mintChanges.put(mint, change);
            }
        }

        // 存储地址变化到模型中供其他处理器使用
        solTransactionModel.setAddressChanges(addressChanges);

        // 2. 寻找我们监控的地址参与的交易
        for (String monitoredAddress : monitoredAddresses) {
            Map<String, BigDecimal> changes = addressChanges.get(monitoredAddress);
            if (changes == null || changes.isEmpty()) {
                continue; // 该监控地址没有参与交易
            }

            // 处理监控地址的每一种代币变化
            for (Map.Entry<String, BigDecimal> entry : changes.entrySet()) {
                String mint = entry.getKey();
                BigDecimal change = entry.getValue();

                MetaSolanaTransactionBo transactionBo = createTransactionBo(
                    solTransactionModel, transaction, mint, change, isTrustworthy);

                if (change.compareTo(BigDecimal.ZERO) > 0) {
                    // 监控地址收到代币 - 寻找发送方
                    String senderAddress = findAddressWithNegativeChange(addressChanges, mint, change.negate());
                    
                    transactionBo.setType("receive");
                    transactionBo.setIssync(0);
                    transactionBo.setAmount(change.abs().divide(getTokenDecimalDivisor(mint), getTokenDecimals(mint), RoundingMode.DOWN));
                    transactionBo.setAddress(solanaHelper.parseAccountToSolAddress(monitoredAddress));
                    transactionBo.setFromaddress(senderAddress != null ? solanaHelper.parseAccountToSolAddress(senderAddress) : "unknown");

                    resultList.add(transactionBo);
                } else {
                    // 监控地址发送代币 - 寻找接收方
                    String receiverAddress = findAddressWithPositiveChange(addressChanges, mint, change.abs());
                    
                    transactionBo.setType("send");
                    transactionBo.setIssync(2);
                    transactionBo.setAmount(change.abs().divide(getTokenDecimalDivisor(mint), getTokenDecimals(mint), RoundingMode.DOWN));
                    transactionBo.setAddress(receiverAddress != null ? solanaHelper.parseAccountToSolAddress(receiverAddress) : "unknown");
                    String monitoredMainAddress = solanaHelper.parseAccountToSolAddress(monitoredAddress);
                    transactionBo.setFromaddress(monitoredMainAddress);

                    resultList.add(transactionBo);
                }
            }
        }

        return resultList;
    }

    /**
     * 分类和标记交易类型
     *
     * @param transactionBoList 交易业务对象列表
     * @param userAddress 用户指定的关联地址
     */
    private void classifyTransactions(List<MetaSolanaTransactionBo> transactionBoList, String userAddress) {
        try {
            Set<String> monitoredAddresses = solanaCstaddressinfoService.queryAllAddressSet();

            for (MetaSolanaTransactionBo transactionBo : transactionBoList) {
                // 设置创建者
                transactionBo.setCreateBy(10086L);

                // 分类交易类型
                if (userAddress != null) {
                    // 如果指定了用户地址，根据用户地址判断
                    if (userAddress.equals(transactionBo.getAddress())) {
                        transactionBo.setType("receive");
                    } else if (userAddress.equals(transactionBo.getFromaddress())) {
                        transactionBo.setType("collect");
                    }
                } else {
                    // 未指定用户地址，使用原有逻辑
                    if (monitoredAddresses.contains(transactionBo.getAddress()) && 
                        !monitoredAddresses.contains(transactionBo.getFromaddress())) {
                        // 接收方是监控地址，发送方不是 -> 充值
                        transactionBo.setType("receive");
                    } else if (monitoredAddresses.contains(transactionBo.getFromaddress())) {
                        // 发送方是监控地址 -> 归集
                        transactionBo.setType("collect");
                    }
                }

                log.debug("交易分类: {} -> {}, from: {}, to: {}", 
                    transactionBo.getTxid(), transactionBo.getType(), 
                    transactionBo.getFromaddress(), transactionBo.getAddress());
            }

        } catch (Exception e) {
            log.error("交易分类失败: {}", e.getMessage());
        }
    }

    // 以下是辅助方法，从原SolanaEventFilterEvent迁移
    private MetaSolanaTransactionBo createTransactionBo(SolanaTransactionModel solTransactionModel, 
                                                       ConfirmedTransaction transaction, 
                                                       String mint, 
                                                       BigDecimal change, 
                                                       boolean isTrustworthy) {
        MetaSolanaTransactionBo transactionBo = new MetaSolanaTransactionBo();
        transactionBo.setContract(mint);
        transactionBo.setTxid(transaction.getTransaction().getSignatures().get(0));
        transactionBo.setBlockheight(solTransactionModel.getSolBlock() != null ?
            (long) (solTransactionModel.getSolBlock().getParentSlot() + 1) : 0);
        transactionBo.setTimestamp(solTransactionModel.getSolBlock() != null ?
            (long) solTransactionModel.getSolBlock().getBlockTime() : System.currentTimeMillis() / 1000);

        // 设置交易手续费
        transactionBo.setFee(BigDecimal.valueOf(transaction.getMeta().getFee())
            .divide(new BigDecimal("**********"), 9, RoundingMode.DOWN));

        transactionBo.setTrustworthy(isTrustworthy);
        return transactionBo;
    }

    private String findAddressWithPositiveChange(Map<String, Map<String, BigDecimal>> addressChanges, 
                                                String mint, BigDecimal amount) {
        if (mint == null || amount == null) {
            return null;
        }

        for (Map.Entry<String, Map<String, BigDecimal>> entry : addressChanges.entrySet()) {
            if (entry.getKey() == null || entry.getValue() == null) {
                continue;
            }

            Map<String, BigDecimal> changes = entry.getValue();
            if (changes.containsKey(mint)) {
                BigDecimal change = changes.get(mint);
                if (change != null && change.compareTo(BigDecimal.ZERO) > 0 &&
                    isApproximatelyEqual(change, amount, new BigDecimal("0.001"))) {
                    return entry.getKey();
                }
            }
        }
        return null;
    }

    private String findAddressWithNegativeChange(Map<String, Map<String, BigDecimal>> addressChanges, 
                                                String mint, BigDecimal amount) {
        for (Map.Entry<String, Map<String, BigDecimal>> entry : addressChanges.entrySet()) {
            Map<String, BigDecimal> changes = entry.getValue();
            if (changes.containsKey(mint)) {
                BigDecimal change = changes.get(mint);
                if (change.compareTo(BigDecimal.ZERO) < 0 &&
                    isApproximatelyEqual(change, amount, new BigDecimal("0.001"))) {
                    return entry.getKey();
                }
            }
        }
        return null;
    }

    private boolean isApproximatelyEqual(BigDecimal a, BigDecimal b, BigDecimal tolerance) {
        return a.subtract(b).abs().compareTo(tolerance) <= 0;
    }

    private ConfirmedTransaction.TokenBalance findMatchingPreBalance(ConfirmedTransaction.TokenBalance post, 
                                                                   List<ConfirmedTransaction.TokenBalance> preBalances) {
        for (ConfirmedTransaction.TokenBalance pre : preBalances) {
            if (pre.getAccountIndex().equals(post.getAccountIndex()) && 
                pre.getMint().equals(post.getMint())) {
                return pre;
            }
        }
        return null;
    }

    private BigDecimal getTokenDecimalDivisor(String mint) {
        // TODO: 实现代币精度获取逻辑
        return new BigDecimal("**********"); // 临时返回SOL的精度
    }

    private int getTokenDecimals(String mint) {
        // TODO: 实现代币精度获取逻辑
        return 9; // 临时返回SOL的精度
    }
}
