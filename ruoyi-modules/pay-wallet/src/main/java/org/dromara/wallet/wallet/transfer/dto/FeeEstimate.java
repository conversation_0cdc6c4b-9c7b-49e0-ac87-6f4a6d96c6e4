package org.dromara.wallet.wallet.transfer.dto;

import java.math.BigDecimal;

/**
 * 统一手续费估算接口
 * 
 * <p>为不同区块链的手续费估算提供统一抽象，支持：</p>
 * <ul>
 *   <li>原生代币手续费需求量</li>
 *   <li>原生代币符号标识</li>
 *   <li>链特定的手续费信息</li>
 * </ul>
 * 
 * <p>各链实现示例：</p>
 * <ul>
 *   <li>TRON: TRX需求量，考虑Energy和Bandwidth</li>
 *   <li>EVM: ETH/BNB等需求量，基于Gas Price和Gas Limit</li>
 *   <li>Solana: SOL需求量，基于交易复杂度</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public interface FeeEstimate {

    /**
     * 获取转账所需的原生代币数量
     * 
     * <p>这是手续费钱包需要提供的最小原生代币数量</p>
     * 
     * @return 原生代币数量，单位为该链的最小单位（如TRX、ETH、SOL）
     */
    BigDecimal getNativeTokenNeeded();

    /**
     * 获取原生代币符号
     * 
     * @return 原生代币符号（如"TRX"、"ETH"、"BNB"、"SOL"）
     */
    String getNativeTokenSymbol();

    /**
     * 获取手续费估算的详细描述
     * 
     * <p>用于日志记录和调试，包含链特定的手续费组成信息</p>
     * 
     * @return 手续费估算描述
     */
    default String getDescription() {
        return String.format("需要 %s %s", getNativeTokenNeeded(), getNativeTokenSymbol());
    }

    /**
     * 判断是否需要手续费
     * 
     * @return true表示需要手续费，false表示免费交易
     */
    default boolean isRequired() {
        return getNativeTokenNeeded().compareTo(BigDecimal.ZERO) > 0;
    }
}
