package org.dromara.wallet.wallet.transfer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 统一转账请求模型
 *
 * <p>用于封装跨链转账的统一请求参数，支持以下特性：</p>
 * <ul>
 *   <li>统一的参数格式，适用于所有支持的区块链</li>
 *   <li>自动原生/合约代币检测</li>
 *   <li>可选的手续费钱包支持配置</li>
 *   <li>扩展性设计，便于添加新的转账参数</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferRequest {
    private String stpValue;

    private String tenantId;
    /**
     * 发送方私钥
     * 必填参数，用于签名交易
     */
    private String privateKey;

    /**
     * 接收方地址
     * 必填参数，转账目标地址
     */
    private String toAddress;

    /**
     * 转账金额（字符串格式）
     * 必填参数，支持小数，如 "1.5"、"100"
     */
    private String amount;

    /**
     * 代币符号
     * 必填参数，如 "TRX"、"USDT"、"BNB"、"SOL"等
     * 系统会自动检测是原生代币还是合约代币
     */
    private String tokenSymbol;

    /**
     * 执行模式
     * 可选参数，默认为异步模式
     * true: 同步执行，等待转账完成后返回结果
     * false: 异步执行，立即返回订单ID，后台处理
     */
    @Builder.Default
    private Boolean syncMode = false;

    /**
     * 区块链名称
     * 必填参数，如 "TRON"、"BSC"、"ARB"、"BASE"、"SOLANA"等
     */
    private String chainName;

    /**
     * 是否启用手续费钱包
     * 可选参数，默认为true
     * 当用户余额不足支付手续费时，是否使用手续费钱包提供资金
     */
    @Builder.Default
    private boolean enableFeeWallet = true;

    /**
     * 转账超时时间（秒）
     * 可选参数，默认为300秒（5分钟）
     * 超过此时间未确认的交易将被视为失败
     */
    @Builder.Default
    private int timeoutSeconds = 300;

    /**
     * 是否等待交易确认
     * 可选参数，默认为true
     * false: 发送交易后立即返回，不等待确认
     * true: 等待交易确认后返回结果
     */
    @Builder.Default
    private boolean waitForConfirmation = true;

    /**
     * 自定义Gas价格（仅EVM链有效）
     * 可选参数，单位为Gwei
     * 如果不设置，将使用链的默认Gas价格策略
     */
    private BigDecimal customGasPrice;

    /**
     * 自定义Gas限制（仅EVM链有效）
     * 可选参数
     * 如果不设置，将使用系统估算的Gas限制
     */
    private Long customGasLimit;

    /**
     * 备注信息
     * 可选参数，用于记录转账用途或其他信息
     */
    private String memo;

    /**
     * 请求ID
     * 可选参数，用于追踪和去重
     * 如果不设置，系统会自动生成
     */
    private String requestId;

    /**
     * 转账业务类型
     * 可选参数，用于标识转账的业务用途
     * 支持的类型：withdraw(提现)、deposit(充值)、collect(归集)、transfer(普通转账)
     * 如果不设置，默认为 "transfer"
     */
    private String businessType;

    // ==================== 便捷构造方法 ====================

    /**
     * 创建基础转账请求
     */
    public static TransferRequest basic(String privateKey, String toAddress,
                                      String amount, String tokenSymbol, String chainName) {
        return TransferRequest.builder()
            .privateKey(privateKey)
            .toAddress(toAddress)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .chainName(chainName)
            .build();
    }

    /**
     * 创建提现转账请求
     */
    public static TransferRequest withdraw(String privateKey, String toAddress,
                                         String amount, String tokenSymbol, String chainName) {
        return TransferRequest.builder()
            .privateKey(privateKey)
            .toAddress(toAddress)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .chainName(chainName)
            .businessType("withdraw")
            .build();
    }

    /**
     * 创建充值转账请求
     */
    public static TransferRequest deposit(String privateKey, String toAddress,
                                        String amount, String tokenSymbol, String chainName) {
        return TransferRequest.builder()
            .privateKey(privateKey)
            .toAddress(toAddress)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .chainName(chainName)
            .businessType("deposit")
            .build();
    }

    /**
     * 创建归集转账请求
     */
    public static TransferRequest collect(String privateKey, String toAddress,
                                        String amount, String tokenSymbol, String chainName) {
        return TransferRequest.builder()
            .privateKey(privateKey)
            .toAddress(toAddress)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .chainName(chainName)
            .businessType("collect")
            .build();
    }

    /**
     * 创建禁用手续费钱包的转账请求
     */
    public static TransferRequest withoutFeeWallet(String privateKey, String toAddress,
                                                 String amount, String tokenSymbol, String chainName) {
        return TransferRequest.builder()
            .privateKey(privateKey)
            .toAddress(toAddress)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .chainName(chainName)
            .enableFeeWallet(false)
            .build();
    }

    /**
     * 创建快速转账请求（不等待确认）
     */
    public static TransferRequest quickTransfer(String privateKey, String toAddress,
                                              String amount, String tokenSymbol, String chainName) {
        return TransferRequest.builder()
            .privateKey(privateKey)
            .toAddress(toAddress)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .chainName(chainName)
            .waitForConfirmation(false)
            .build();
    }

    // ==================== 验证方法 ====================

    /**
     * 验证请求参数的完整性
     */
    public void validate() {
        if (privateKey == null || privateKey.trim().isEmpty()) {
            throw new IllegalArgumentException("私钥不能为空");
        }
        if (toAddress == null || toAddress.trim().isEmpty()) {
            throw new IllegalArgumentException("接收地址不能为空");
        }
        if (amount == null || amount.trim().isEmpty()) {
            throw new IllegalArgumentException("转账金额不能为空");
        }
        if (tokenSymbol == null || tokenSymbol.trim().isEmpty()) {
            throw new IllegalArgumentException("代币符号不能为空");
        }
        if (chainName == null || chainName.trim().isEmpty()) {
            throw new IllegalArgumentException("链名称不能为空");
        }

        // 验证金额格式
        try {
            BigDecimal amountValue = new BigDecimal(amount);
            if (amountValue.compareTo(BigDecimal.ZERO) <= 0) {
                throw new IllegalArgumentException("转账金额必须大于0");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("转账金额格式不正确: " + amount);
        }

        // 验证超时时间
        if (timeoutSeconds <= 0) {
            throw new IllegalArgumentException("超时时间必须大于0");
        }
    }

    /**
     * 获取转账金额的BigDecimal格式
     */
    public BigDecimal getAmountAsBigDecimal() {
        return new BigDecimal(amount);
    }

    /**
     * 检查是否为原生代币转账
     * 注意：这里只是基于常见的原生代币符号进行判断
     * 具体的检测逻辑应该在各个链的策略实现中进行
     */
    public boolean isLikelyNativeToken() {
        String upperSymbol = tokenSymbol.toUpperCase();
        String upperChain = chainName.toUpperCase();

        // 常见的原生代币映射
        return switch (upperChain) {
            case "TRON" -> "TRX".equals(upperSymbol);
            case "BSC" -> "BNB".equals(upperSymbol);
            case "ETH", "ETHEREUM" -> "ETH".equals(upperSymbol);
            case "ARB", "ARBITRUM" -> "ETH".equals(upperSymbol);
            case "BASE" -> "ETH".equals(upperSymbol);
            case "SOLANA" -> "SOL".equals(upperSymbol);
            default -> false;
        };
    }
}
