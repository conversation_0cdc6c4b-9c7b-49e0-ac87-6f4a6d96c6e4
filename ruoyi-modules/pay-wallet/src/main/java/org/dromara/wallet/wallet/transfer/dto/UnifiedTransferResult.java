package org.dromara.wallet.wallet.transfer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 统一转账结果
 *
 * <p>统一的转账结果类型，支持同步和异步两种模式：</p>
 * <ul>
 *   <li>同步模式：包含完整的转账执行结果</li>
 *   <li>异步模式：包含订单信息和状态查询方式</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedTransferResult {

    // ==================== 基础信息 ====================

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行模式
     * true: 同步模式
     * false: 异步模式
     */
    private boolean syncMode;

    // ==================== 订单信息 ====================

    /**
     * 订单ID（异步模式必有，同步模式可选）
     */
    private Long orderId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 区块链名称
     */
    private String chainName;

    /**
     * 转账类型
     */
    private String transferType;

    // ==================== 转账结果（同步模式） ====================

    /**
     * 交易哈希
     */
    private String txHash;

    /**
     * 发送方地址
     */
    private String fromAddress;

    /**
     * 接收方地址
     */
    private String toAddress;

    /**
     * 转账金额
     */
    private BigDecimal amount;

    /**
     * 代币符号
     */
    private String tokenSymbol;

    /**
     * 是否提供了手续费
     */
    private Boolean feeProvided;

    /**
     * 手续费金额
     */
    private BigDecimal feeAmount;

    /**
     * 确认结果
     */
    private TransactionConfirmationResult confirmationResult;

    // ==================== 异步信息（异步模式） ====================

    /**
     * 预计处理时间（分钟）
     */
    private Integer estimatedProcessingMinutes;

    /**
     * 创建时间
     */
    private Date createTime;

    // ==================== 静态工厂方法 ====================

    /**
     * 创建同步成功结果
     */
    public static UnifiedTransferResult syncSuccess(BlockchainTransferResult blockchainResult) {
        return UnifiedTransferResult.builder()
            .success(true)
            .syncMode(true)
            .txHash(blockchainResult.getTxHash())
            .fromAddress(blockchainResult.getFromAddress())
            .toAddress(blockchainResult.getToAddress())
            .amount(blockchainResult.getTransferAmount())
            .tokenSymbol(blockchainResult.getTransferTokenSymbol())
            .feeProvided(blockchainResult.isFeeProvided())
            .feeAmount(blockchainResult.getFeeAmount())
            .confirmationResult(blockchainResult.getConfirmationResult())
            .chainName(blockchainResult.getChainName())
            .build();
    }

    /**
     * 创建同步失败结果
     */
    public static UnifiedTransferResult syncFailure(String chainName, String errorCode, String errorMessage) {
        return UnifiedTransferResult.builder()
            .success(false)
            .syncMode(true)
            .chainName(chainName)
            .errorCode(errorCode)
            .errorMessage(errorMessage)
            .build();
    }

    /**
     * 创建异步成功结果
     */
    public static UnifiedTransferResult asyncSuccess(AsyncTransferResult asyncResult) {
        // 转换 LocalDateTime 到 Date
        Date createTime = null;
        if (asyncResult.getCreateTime() != null) {
            createTime = java.sql.Timestamp.valueOf(asyncResult.getCreateTime());
        }

        return UnifiedTransferResult.builder()
            .success(true)
            .syncMode(false)
            .orderId(asyncResult.getOrderId())
            .requestId(asyncResult.getRequestId())
            .chainName(asyncResult.getChainName())
            .transferType(asyncResult.getTransferType())
            .estimatedProcessingMinutes(asyncResult.getEstimatedProcessingMinutes())
            .createTime(createTime)
            .build();
    }

    /**
     * 创建异步失败结果
     */
    public static UnifiedTransferResult asyncFailure(String chainName, String errorCode, String errorMessage) {
        return UnifiedTransferResult.builder()
            .success(false)
            .syncMode(false)
            .chainName(chainName)
            .errorCode(errorCode)
            .errorMessage(errorMessage)
            .build();
    }

    // ==================== 辅助方法 ====================

    /**
     * 是否可以查询状态（仅异步模式）
     */
    public boolean canQueryStatus() {
        return !syncMode && success && orderId != null;
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (syncMode) {
            return success ? "转账执行成功" : "转账执行失败: " + errorMessage;
        } else {
            if (success) {
                return String.format("转账订单已创建，订单ID: %d，预计%d分钟内完成处理",
                    orderId, estimatedProcessingMinutes);
            } else {
                return "订单创建失败: " + errorMessage;
            }
        }
    }

    /**
     * 获取格式化信息
     */
    public String getFormattedInfo() {
        if (syncMode) {
            return String.format("同步转账[%s]: %s, txHash=%s",
                chainName, success ? "成功" : "失败", txHash);
        } else {
            return String.format("异步转账[%s]: %s, orderId=%d, requestId=%s",
                chainName, success ? "订单已创建" : "订单创建失败", orderId, requestId);
        }
    }
}
