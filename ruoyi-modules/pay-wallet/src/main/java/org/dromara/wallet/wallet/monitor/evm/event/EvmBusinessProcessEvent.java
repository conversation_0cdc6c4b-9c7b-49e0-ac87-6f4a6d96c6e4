package org.dromara.wallet.wallet.monitor.evm.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.monitor.EthMonitorEvent;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.config.facade.ArbConfigFacade;
import org.dromara.wallet.config.facade.BaseConfigFacade;
import org.dromara.wallet.config.facade.BscConfigFacade;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.dromara.wallet.domain.bo.MetaArbTransactionBo;
import org.dromara.wallet.domain.bo.MetaBaseTransactionBo;
import org.dromara.wallet.domain.bo.MetaBep20TransactionBo;
import org.dromara.wallet.service.IMetaArbTransactionService;
import org.dromara.wallet.service.IMetaBaseTransactionService;
import org.dromara.wallet.service.IMetaBep20CstaddressinfoService;
import org.dromara.wallet.service.IMetaBep20TransactionService;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.dromara.wallet.wallet.monitor.evm.dto.EvmTransactionModel;
import org.dromara.wallet.wallet.monitor.evm.event.EvmAddressFilterEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.web3j.protocol.core.methods.response.Log;
import org.web3j.utils.Numeric;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * EVM业务处理事件
 * 负责处理过滤后的事件日志，执行完整的业务逻辑
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>交易解析：解析Transfer事件，提取交易信息</li>
 *   <li>交易分类：根据监控地址分类交易类型（receive/send/collect/unknown）</li>
 *   <li>业务验证：执行最小金额检查和交易可信度验证</li>
 *   <li>数据保存：保存交易记录到对应的数据库表</li>
 *   <li>多链支持：支持BSC、ARB、BASE等EVM链</li>
 *   <li>错误处理：完善的异常处理和重试机制</li>
 *   <li>性能监控：记录业务处理的性能指标</li>
 * </ul>
 *
 * <p>处理流程：</p>
 * <ol>
 *   <li>检查事件过滤器结果</li>
 *   <li>解析交易信息（Transfer事件）</li>
 *   <li>分类交易类型</li>
 *   <li>根据类型执行相应的业务处理</li>
 *   <li>记录性能指标</li>
 * </ol>
 *
 * <p>重构说明：</p>
 * <ul>
 *   <li>移除对EvmTransactionManager的依赖，实现自包含的业务处理</li>
 *   <li>将完整的业务处理逻辑从EvmTransactionManager移动到此处</li>
 *   <li>包含交易解析、验证、分类、保存等完整功能</li>
 *   <li>消除循环依赖，提高代码的可维护性</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EvmBusinessProcessEvent implements EthMonitorEvent {

    // 业务处理相关服务
    private final IMetaArbTransactionService metaArbTransactionService;
    private final IMetaBaseTransactionService metaBaseTransactionService;
    private final IMetaBep20TransactionService metaBep20TransactionService;
    private final IMetaBep20CstaddressinfoService evmAddressService;
    private final EvmHelper evmHelper;

    /**
     * 链配置门面映射表
     * key: 链名称 (BSC, ARB, BASE)
     * value: 对应的EvmConfigFacade实现
     */
    private final Map<String, EvmConfigFacade> configFacadeMap;

    /**
     * 构造函数，自动注入所有EvmConfigFacade实现并构建映射表
     * 重构说明：移除对EvmTransactionManager的依赖，直接注入业务处理所需的服务
     */
    @Autowired
    public EvmBusinessProcessEvent(IMetaArbTransactionService metaArbTransactionService,
                                  IMetaBaseTransactionService metaBaseTransactionService,
                                  IMetaBep20TransactionService metaBep20TransactionService,
                                  IMetaBep20CstaddressinfoService evmAddressService,
                                  EvmHelper evmHelper,
                                  List<EvmConfigFacade> configFacades) {
        this.metaArbTransactionService = metaArbTransactionService;
        this.metaBaseTransactionService = metaBaseTransactionService;
        this.metaBep20TransactionService = metaBep20TransactionService;
        this.evmAddressService = evmAddressService;
        this.evmHelper = evmHelper;
        this.configFacadeMap = configFacades.stream()
            .collect(Collectors.toMap(
                EvmConfigFacade::getChainName,
                Function.identity()
            ));

        log.info("EvmBusinessProcessEvent初始化完成，支持的链: {}", configFacadeMap.keySet());
    }

    /**
     * ERC20 Transfer事件的topic签名
     * keccak256("Transfer(address,address,uint256)")
     */
    private static final String TRANSFER_EVENT_TOPIC = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";

    @Override
    public EthMonitorFilter ethMonitorFilter() {
        // 不在这里进行过滤，接受所有交易进行业务判断
        return EthMonitorFilter.builder()
            .setMinValue(BigInteger.ZERO);
    }

    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 1. 检查是否为EVM交易模型
            if (transactionModel.getEthTransactionModel() == null) {
                log.debug("{}链跳过非EVM交易模型", getChainType(transactionModel));
                return;
            }

            // 2. 获取链类型和配置门面
            String chainType = transactionModel.getChainType();
            if (chainType == null) {
                log.warn("交易模型缺少链类型信息，跳过业务处理");
                return;
            }

            EvmConfigFacade configFacade = configFacadeMap.get(chainType.toUpperCase());
            if (configFacade == null) {
                log.warn("未找到{}链的配置门面，跳过业务处理", chainType);
                return;
            }

            // 3. 检查地址过滤结果
            Boolean addressFilterPassed = EvmAddressFilterEvent.getAddressFilterResult(transactionModel);
            if (addressFilterPassed != null && !addressFilterPassed) {
                log.debug("{}链交易未通过地址过滤检查，跳过业务处理", chainType);
                return;
            }

            // 4. 获取过滤后的Log
            Log filteredLog = EvmEventFilterEvent.getFilteredLog(transactionModel);
            if (filteredLog == null) {
                log.debug("{}链交易没有过滤后的事件日志，跳过业务处理", chainType);
                return;
            }

            String txHash = filteredLog.getTransactionHash();
            if (txHash == null) {
                log.debug("{}链交易哈希为空，跳过业务处理", chainType);
                return;
            }

            // 5. 执行业务处理
            processBusinessLogic(filteredLog, configFacade, txHash);

        } catch (Exception e) {
            log.error("{}链业务处理失败: txHash={}, error={}",
                getChainType(transactionModel),
                getTransactionHash(transactionModel),
                e.getMessage());
            // 不重新抛出异常，避免影响其他交易的处理
        }
    }

    /**
     * 执行业务逻辑处理
     * 重构说明：从EvmTransactionManager移动过来的完整业务处理逻辑
     */
    private void processBusinessLogic(Log filteredLog, EvmConfigFacade configFacade, String txHash) {
        long startTime = System.currentTimeMillis();

        try {
            log.debug("{}链交易{}开始业务处理", configFacade.getChainName(), txHash);

            // 1. 解析交易
            EvmTransactionModel transaction = parseTransaction(filteredLog, configFacade);
            if (transaction == null) {
                log.debug("{}链交易{}解析失败，跳过处理", configFacade.getChainName(), txHash);
                return;
            }

            // 2. 分类交易类型
            classifyTransaction(transaction, configFacade);

            // todo 检查该数据是否已存在数据库

            // 3. 根据交易类型处理
            switch (transaction.getTransactionType()) {
                case "receive":
                    handleReceiveTransaction(transaction, configFacade);
                    break;
                case "send":
                    handleSendTransaction(transaction, configFacade);
                    break;
                case "collect":
                    handleCollectTransaction(transaction, configFacade);
                    break;
                case "unknown":
                    log.debug("{}链交易{}类型为unknown，跳过处理",
                        configFacade.getChainName(), transaction.getTransactionHash());
                    break;
                default:
                    log.debug("{}链交易{}类型未知，跳过处理",
                        configFacade.getChainName(), transaction.getTransactionHash());
                    transaction.setTransactionType("unknown");
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.debug("{}链交易{}业务处理完成，耗时: {}ms",
                configFacade.getChainName(), txHash, processingTime);

            // 记录性能指标
            recordPerformanceMetrics(configFacade.getChainName(), 1, processingTime);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("{}链交易{}业务处理失败，耗时: {}ms, 错误: {}",
                configFacade.getChainName(), txHash, processingTime, e.getMessage());

            // 记录失败指标
            recordFailureMetrics(configFacade.getChainName(), 1, processingTime, e);

            // 重新抛出异常，让上层处理
            throw e;
        }
    }

    // ============ 业务处理方法（从EvmTransactionManager移动） ============

    /**
     * 解析交易
     * 从EvmTransactionManager移动过来的方法
     */
    private EvmTransactionModel parseTransaction(Log transactionLog, EvmConfigFacade evmConfigFacade) {
        String txHash = transactionLog.getTransactionHash();
        List<String> topics = transactionLog.getTopics();

        // 验证是否为Transfer事件
        if (topics == null || topics.size() < 3) {
            log.debug("{}链交易{}的topics不符合Transfer事件要求", evmConfigFacade.getChainName(), txHash);
            return null;
        }

        if (!TRANSFER_EVENT_TOPIC.equals(topics.get(0))) {
            log.debug("{}链交易{}不是Transfer事件", evmConfigFacade.getChainName(), txHash);
            return null;
        }

        try {
            // 创建交易模型
            EvmTransactionModel transactionModel = EvmTransactionModel.fromLog(transactionLog);

            // 解析Transfer事件
            parseTransferEvent(transactionModel, evmConfigFacade);

            // 设置链信息
            transactionModel.setChainName(evmConfigFacade.getChainName());
            transactionModel.setChainId(evmConfigFacade.getChainId());

            log.debug("{}链成功解析Transfer事件: from={}, to={}, contract={}, amount={}, txHash={}",
                evmConfigFacade.getChainName(),
                transactionModel.getFromAddress(),
                transactionModel.getToAddress(),
                transactionModel.getContractAddress(),
                transactionModel.getAmount(),
                txHash);

            return transactionModel;

        } catch (Exception e) {
            log.error("{}链解析交易{}失败: {}", evmConfigFacade.getChainName(), txHash, e.getMessage());
            return null;
        }
    }

    /**
     * 解析Transfer事件
     * 从EvmTransactionManager移动过来的方法
     */
    private void parseTransferEvent(EvmTransactionModel transactionModel, EvmConfigFacade evmConfigFacade) {
        Log transactionLog = transactionModel.getOriginalLog();
        List<String> topics = transactionLog.getTopics();

        // 解析from地址（去掉前缀0x000...）
        String fromAddress = "0x" + topics.get(1).substring(26);
        transactionModel.setFromAddress(fromAddress.toLowerCase());

        // 解析to地址（去掉前缀0x000...）
        String toAddress = "0x" + topics.get(2).substring(26);
        transactionModel.setToAddress(toAddress.toLowerCase());

        // 解析转账金额
        String data = transactionLog.getData();
        if (data != null && !data.equals("0x")) {
            BigInteger rawAmount = Numeric.toBigInt(data);
            transactionModel.setRawAmount(rawAmount);

            // 获取代币信息并转换精度
            String contractAddress = transactionLog.getAddress();
            String tokenSymbol = getTokenSymbol(contractAddress, evmConfigFacade);
            Integer tokenDecimals = getTokenDecimals(contractAddress, evmConfigFacade);

            transactionModel.setTokenSymbol(tokenSymbol);
            transactionModel.setTokenDecimals(tokenDecimals);

            // 转换金额精度
            BigDecimal amount = new BigDecimal(rawAmount)
                .divide(BigDecimal.TEN.pow(tokenDecimals), tokenDecimals, RoundingMode.DOWN);
            transactionModel.setAmount(amount);
        }

        // 设置时间戳（从区块信息获取真实的区块时间戳）
        setTransactionTimestamp(transactionModel, evmConfigFacade);

        // 设置交易手续费（从交易回执获取真实的手续费）
        setTransactionFee(transactionModel, evmConfigFacade);
    }

    /**
     * 获取代币符号
     * 从EvmTransactionManager移动过来的方法
     */
    private String getTokenSymbol(String contractAddress, EvmConfigFacade evmConfigFacade) {
        try {
            // 通过合约地址反查代币符号
            Set<String> enabledTokens = evmConfigFacade.getEnabledTokenSymbols();
            for (String tokenSymbol : enabledTokens) {
                String configAddress = evmConfigFacade.getContractAddress(tokenSymbol);
                if (contractAddress.equalsIgnoreCase(configAddress)) {
                    return tokenSymbol;
                }
            }
            return "UNKNOWN";
        } catch (Exception e) {
            log.warn("{}链获取合约{}的代币符号失败: {}",
                evmConfigFacade.getChainName(), contractAddress, e.getMessage());
            return "UNKNOWN";
        }
    }

    /**
     * 获取代币精度
     * 从EvmTransactionManager移动过来的方法
     */
    private Integer getTokenDecimals(String contractAddress, EvmConfigFacade evmConfigFacade) {
        try {
            // 通过合约地址反查代币精度
            String tokenSymbol = getTokenSymbol(contractAddress, evmConfigFacade);
            if (!"UNKNOWN".equals(tokenSymbol)) {
                return evmConfigFacade.getTokenDecimals(tokenSymbol);
            }
            return 18; // 默认18位精度
        } catch (Exception e) {
            log.warn("{}链获取合约{}的代币精度失败: {}",
                evmConfigFacade.getChainName(), contractAddress, e.getMessage());
            return 18; // 默认18位精度
        }
    }

    /**
     * 设置交易时间戳
     * 从EvmTransactionManager移动过来的方法
     */
    private void setTransactionTimestamp(EvmTransactionModel transactionModel, EvmConfigFacade evmConfigFacade) {
        try {
            Log transactionLog = transactionModel.getOriginalLog();
            BigInteger blockNumber = transactionLog.getBlockNumber();
            if (blockNumber != null) {
                org.web3j.protocol.core.methods.response.EthBlock.Block block =
                    evmHelper.getBlockByNumber(blockNumber, evmConfigFacade);
                if (block != null && block.getTimestamp() != null) {
                    // EVM 区块时间戳是秒级的
                    transactionModel.setTimestamp(block.getTimestamp().longValue());
                } else {
                    log.warn("{}链无法获取区块{}的时间戳，使用当前时间",
                        evmConfigFacade.getChainName(), blockNumber);
                    transactionModel.setTimestamp(System.currentTimeMillis() / 1000);
                }
            } else {
                log.warn("{}链交易{}缺少区块号，使用当前时间",
                    evmConfigFacade.getChainName(), transactionLog.getTransactionHash());
                transactionModel.setTimestamp(System.currentTimeMillis() / 1000);
            }
        } catch (Exception e) {
            log.warn("{}链获取区块时间戳失败，使用当前时间: {}",
                evmConfigFacade.getChainName(), e.getMessage());
            transactionModel.setTimestamp(System.currentTimeMillis() / 1000);
        }
    }

    /**
     * 设置交易手续费
     * 从EvmTransactionManager移动过来的方法
     */
    private void setTransactionFee(EvmTransactionModel transactionModel, EvmConfigFacade evmConfigFacade) {
        try {
            Log transactionLog = transactionModel.getOriginalLog();
            String transactionHash = transactionLog.getTransactionHash();
            if (transactionHash != null) {
                org.web3j.protocol.core.methods.response.TransactionReceipt receipt =
                    evmHelper.getTransactionReceipt(transactionHash, evmConfigFacade);
                if (receipt != null) {
                    // 计算实际支付的手续费：gasUsed * effectiveGasPrice
                    BigInteger gasUsed = receipt.getGasUsed();
                    BigInteger effectiveGasPrice = parseHexToBigInteger(receipt.getEffectiveGasPrice());

                    if (gasUsed != null && effectiveGasPrice != null) {
                        // 计算手续费（Wei单位）
                        BigInteger feeInWei = gasUsed.multiply(effectiveGasPrice);

                        // 转换为ETH/BNB等原生代币单位（Wei转换为Ether，18位小数）
                        BigDecimal feeInNativeToken = new BigDecimal(feeInWei)
                            .divide(new BigDecimal("1000000000000000000"), 18, RoundingMode.DOWN);

                        transactionModel.setTransactionFee(feeInNativeToken);
                        transactionModel.setGasUsed(gasUsed);
                        transactionModel.setGasPrice(effectiveGasPrice);

                        log.debug("{}链交易{}手续费计算成功: gasUsed={}, effectiveGasPrice={}, fee={}",
                            evmConfigFacade.getChainName(), transactionHash, gasUsed, effectiveGasPrice, feeInNativeToken);
                    } else {
                        log.warn("{}链交易{}回执中缺少gas信息: gasUsed={}, effectiveGasPrice={}",
                            evmConfigFacade.getChainName(), transactionHash, gasUsed, effectiveGasPrice);
                    }
                } else {
                    log.warn("{}链无法获取交易{}的回执信息",
                        evmConfigFacade.getChainName(), transactionHash);
                }
            } else {
                log.warn("{}链交易缺少交易哈希", evmConfigFacade.getChainName());
            }
        } catch (Exception e) {
            log.warn("{}链获取交易手续费失败: {}",
                evmConfigFacade.getChainName(), e.getMessage());
        }
    }

    /**
     * 解析十六进制字符串为 BigInteger
     * 从EvmTransactionManager移动过来的方法
     */
    private BigInteger parseHexToBigInteger(String hexString) {
        if (hexString == null || hexString.trim().isEmpty()) {
            return null;
        }
        try {
            // 移除0x前缀（如果存在）
            if (hexString.startsWith("0x") || hexString.startsWith("0X")) {
                hexString = hexString.substring(2);
            }
            return new BigInteger(hexString, 16);
        } catch (NumberFormatException e) {
            log.warn("解析十六进制字符串失败: {}", hexString);
            return null;
        }
    }

    /**
     * 分类交易类型
     * 从EvmTransactionManager移动过来的方法
     */
    private void classifyTransaction(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        String fromAddress = transaction.getFromAddress();
        String toAddress = transaction.getToAddress();

        // 获取监控地址列表
        Set<String> monitoredAddresses = TenantHelper.ignore(evmAddressService::queryAllAddressSet);

        if (monitoredAddresses.contains(toAddress)) {
            // 监控地址接收代币
            transaction.setTransactionType("receive");
        } else if (monitoredAddresses.contains(fromAddress)) {
            // 监控地址发送代币
            transaction.setTransactionType("send");
        } else {
            // 非监控地址交易，可能是内部转账或其他
            transaction.setTransactionType("unknown");
        }

        log.debug("{}链交易{}分类为: {}",
            configFacade.getChainName(),
            transaction.getTransactionHash(),
            transaction.getTransactionType());
    }

    /**
     * 处理接收交易
     * 从EvmTransactionManager移动过来的方法（简化版本）
     */
    private void handleReceiveTransaction(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        log.info("{}链开始处理接收交易: {}", configFacade.getChainName(), transaction.getFormattedInfo());

        try {
            // 1. 执行验证逻辑
            if (!checkMinimumDepositAmount(transaction, configFacade)) {
                // 金额不符合要求，设置为验证失败
                transaction.setProcessStatus(4);
                transaction.setErrorMessage("金额低于最小入账金额");
                saveTransactionRecord(transaction, configFacade);
                log.info("{}链接收交易验证失败（最小金额）: {}", configFacade.getChainName(), transaction.getTransactionHash());
                return;
            }

            if (!verifyTransactionTrustworthiness(transaction, configFacade)) {
                // 交易不可信，设置为验证失败
                transaction.setProcessStatus(5);
                transaction.setErrorMessage("交易可信度验证失败");
                saveTransactionRecord(transaction, configFacade);
                log.info("{}链接收交易验证失败（可信度）: {}", configFacade.getChainName(), transaction.getTransactionHash());
                return;
            }

            // 2. 验证通过，保存交易记录到数据库，设置状态为待处理
            transaction.setProcessStatus(0);
            saveTransactionRecord(transaction, configFacade);

            log.info("{}链接收交易验证通过，记录保存成功: {}", configFacade.getChainName(), transaction.getTransactionHash());

        } catch (Exception e) {
            log.error("{}链处理接收交易失败: {}, 错误信息: {}",
                configFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage());

            // 保持待处理状态，等待重试
            transaction.setProcessStatus(0);
            transaction.setErrorMessage(e.getMessage());

            throw e;
        }
    }

    /**
     * 处理发送交易
     * 从EvmTransactionManager移动过来的方法（简化版本）
     */
    private void handleSendTransaction(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        log.info("{}链处理发送交易: {}", configFacade.getChainName(), transaction.getFormattedInfo());

        try {
            // 发送交易直接保存记录，设置状态为处理完成
            transaction.setProcessStatus(1);
            saveTransactionRecord(transaction, configFacade);

            log.info("{}链发送交易记录保存完成: {}", configFacade.getChainName(), transaction.getTransactionHash());

        } catch (Exception e) {
            log.error("{}链处理发送交易失败: {}, 错误信息: {}",
                configFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage());

            // 保持待处理状态，等待重试
            transaction.setProcessStatus(0);
            transaction.setErrorMessage(e.getMessage());

            throw e;
        }
    }

    /**
     * 处理归集交易
     * 从EvmTransactionManager移动过来的方法（简化版本）
     */
    private void handleCollectTransaction(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        log.info("{}链处理归集交易: {}", configFacade.getChainName(), transaction.getFormattedInfo());

        try {
            // 归集交易直接保存记录，设置状态为处理完成
            transaction.setProcessStatus(1);
            saveTransactionRecord(transaction, configFacade);

            log.info("{}链归集交易记录保存完成: {}", configFacade.getChainName(), transaction.getTransactionHash());

        } catch (Exception e) {
            log.error("{}链处理归集交易失败: {}, 错误信息: {}",
                configFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage(), e);

            // 保持待处理状态，等待重试
            transaction.setProcessStatus(0);
            transaction.setErrorMessage(e.getMessage());

            throw e;
        }
    }

    // ============ 验证和保存方法（从EvmTransactionManager移动，简化版本） ============

    /**
     * 检查最小入账金额
     * 从EvmTransactionManager移动过来的方法（简化版本）
     */
    private boolean checkMinimumDepositAmount(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        try {
            log.debug("{}链检查最小入账金额: txHash={}, amount={}, token={}",
                configFacade.getChainName(), transaction.getTransactionHash(),
                transaction.getAmount(), transaction.getTokenSymbol());

            // 获取交易金额和代币符号
            BigDecimal transactionAmount = transaction.getAmount();
            String tokenSymbol = transaction.getTokenSymbol();

            if (transactionAmount == null || tokenSymbol == null) {
                log.warn("{}链交易{}缺少必要信息: amount={}, token={}",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    transactionAmount, tokenSymbol);
                return false;
            }

            // 获取最小入账金额配置
            BigDecimal minDepositAmount = getMinDepositAmount(tokenSymbol, configFacade);
            if (minDepositAmount == null) {
                log.warn("{}链无法获取代币{}的最小入账金额配置，跳过验证",
                    configFacade.getChainName(), tokenSymbol);
                return true; // 无配置时默认通过
            }

            // 比较交易金额与最小金额
            boolean isValid = transactionAmount.compareTo(minDepositAmount) >= 0;

            if (!isValid) {
                log.info("{}链交易{}金额过小: {} {} < {} {}",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    transactionAmount, tokenSymbol, minDepositAmount, tokenSymbol);
            } else {
                log.debug("{}链交易{}金额验证通过: {} {} >= {} {}",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    transactionAmount, tokenSymbol, minDepositAmount, tokenSymbol);
            }

            return isValid;

        } catch (Exception e) {
            log.error("{}链检查最小入账金额失败: txHash={}, error={}",
                configFacade.getChainName(), transaction.getTransactionHash(), e.getMessage(), e);
            return true; // 出错时默认通过，避免阻塞正常交易
        }
    }

    /**
     * 验证交易可信度
     * 从EvmTransactionManager移动过来的方法（简化版本）
     */
    private boolean verifyTransactionTrustworthiness(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        try {
            log.debug("{}链验证交易可信度: txHash={}, to={}, amount={}, token={}",
                configFacade.getChainName(), transaction.getTransactionHash(),
                transaction.getToAddress(), transaction.getAmount(), transaction.getTokenSymbol());

            // 获取交易信息
            String toAddress = transaction.getToAddress();
            BigDecimal transactionAmount = transaction.getAmount();
            String tokenSymbol = transaction.getTokenSymbol();

            if (toAddress == null || transactionAmount == null || tokenSymbol == null) {
                log.warn("{}链交易{}缺少必要信息: to={}, amount={}, token={}",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    toAddress, transactionAmount, tokenSymbol);
                return false;
            }

            // 查询接收地址的当前余额
            BigDecimal currentBalance = evmHelper.balanceGetForRead(toAddress, tokenSymbol, configFacade);
            if (currentBalance == null) {
                log.warn("{}链无法查询地址{}的{}余额，可信度验证失败",
                    configFacade.getChainName(), toAddress, tokenSymbol);
                return false;
            }

            // 验证余额是否符合预期：当前余额应该 >= 交易金额
            boolean isTrustworthy = currentBalance.compareTo(transactionAmount) >= 0;

            if (!isTrustworthy) {
                log.warn("{}链交易{}可信度验证失败: 当前余额({} {}) < 交易金额({} {})",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    currentBalance, tokenSymbol, transactionAmount, tokenSymbol);
            } else {
                log.debug("{}链交易{}可信度验证通过: 当前余额({} {}) >= 交易金额({} {})",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    currentBalance, tokenSymbol, transactionAmount, tokenSymbol);
            }

            return isTrustworthy;

        } catch (Exception e) {
            log.error("{}链验证交易可信度失败: txHash={}, error={}",
                configFacade.getChainName(), transaction.getTransactionHash(), e.getMessage(), e);
            return true; // 出错时返回true，避免因网络问题等临时错误阻塞正常交易
        }
    }

    /**
     * 获取最小入账金额配置
     * 从EvmTransactionManager移动过来的方法
     */
    private BigDecimal getMinDepositAmount(String tokenSymbol, EvmConfigFacade configFacade) {
        try {
            // 使用instanceof判断具体的配置类型，调用相应的方法
            if (configFacade instanceof BscConfigFacade bscFacade) {
                return bscFacade.getMinTransferAmount(tokenSymbol);
            } else if (configFacade instanceof ArbConfigFacade arbFacade) {
                return arbFacade.getMinTransferAmount(tokenSymbol);
            } else if (configFacade instanceof BaseConfigFacade baseFacade) {
                return baseFacade.getMinTransferAmount(tokenSymbol);
            } else {
                log.warn("不支持的EVM配置类型: {}", configFacade.getClass().getSimpleName());
                return null;
            }
        } catch (Exception e) {
            log.error("获取{}链代币{}的最小入账金额失败: {}",
                configFacade.getChainName(), tokenSymbol, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 保存交易记录到数据库
     * 从EvmTransactionManager移动过来的方法（简化版本）
     */
    private void saveTransactionRecord(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        String chainName = configFacade.getChainName();
        String txHash = transaction.getTransactionHash();

        try {
            log.debug("开始保存{}链交易记录: {}", chainName, txHash);

            // 根据链名称简单判断使用哪个Service
            if ("BSC".equalsIgnoreCase(chainName)) {
                saveBscTransaction(transaction);
            } else if ("ARB".equalsIgnoreCase(chainName)) {
                saveArbTransaction(transaction);
            } else if ("BASE".equalsIgnoreCase(chainName)) {
                saveBaseTransaction(transaction);
            } else {
                log.error("不支持的链类型: {}", chainName);
                throw new RuntimeException("不支持的链类型: " + chainName);
            }

            log.info("{}链交易记录保存成功: txid={}, amount={}, type={}",
                chainName, txHash, transaction.getAmount(), transaction.getTransactionType());

        } catch (Exception e) {
            log.error("保存{}链交易记录失败: {}, 错误信息: {}", chainName, txHash, e.getMessage());
            throw new RuntimeException("保存交易记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存BSC链交易记录
     */
    private void saveBscTransaction(EvmTransactionModel transaction) {
        try {
            // 转换为BSC业务对象
            MetaBep20TransactionBo bo = convertToBscBo(transaction);

            // 保存到数据库
            Boolean result = metaBep20TransactionService.insertByBo(bo);
            if (!Boolean.TRUE.equals(result)) {
                throw new RuntimeException("BSC交易保存失败");
            }
        } catch (Exception e) {
            log.error("保存BSC交易记录失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 保存ARB链交易记录
     */
    private void saveArbTransaction(EvmTransactionModel transaction) {
        try {
            // 转换为ARB业务对象
            MetaArbTransactionBo bo = convertToArbBo(transaction);

            // 保存到数据库
            Boolean result = metaArbTransactionService.insertByBo(bo);
            if (!Boolean.TRUE.equals(result)) {
                throw new RuntimeException("ARB交易保存失败");
            }
        } catch (Exception e) {
            log.error("保存ARB交易记录失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 保存BASE链交易记录
     */
    private void saveBaseTransaction(EvmTransactionModel transaction) {
        try {
            // 转换为BASE业务对象
            MetaBaseTransactionBo bo = convertToBaseBo(transaction);

            // 保存到数据库
            Boolean result = metaBaseTransactionService.insertByBo(bo);
            if (!Boolean.TRUE.equals(result)) {
                throw new RuntimeException("BASE交易保存失败");
            }
        } catch (Exception e) {
            log.error("保存BASE交易记录失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 转换为BSC业务对象
     */
    private MetaBep20TransactionBo convertToBscBo(EvmTransactionModel transaction) {
        MetaBep20TransactionBo bo = new MetaBep20TransactionBo();
        fillCommonFields(bo, transaction);
        return bo;
    }

    /**
     * 转换为ARB业务对象
     */
    private MetaArbTransactionBo convertToArbBo(EvmTransactionModel transaction) {
        MetaArbTransactionBo bo = new MetaArbTransactionBo();
        fillCommonFields(bo, transaction);
        return bo;
    }

    /**
     * 转换为BASE业务对象
     */
    private MetaBaseTransactionBo convertToBaseBo(EvmTransactionModel transaction) {
        MetaBaseTransactionBo bo = new MetaBaseTransactionBo();
        fillCommonFields(bo, transaction);
        return bo;
    }

    /**
     * 填充通用字段
     * 从EvmTransactionManager移动过来的方法
     */
    private void fillCommonFields(Object bo, EvmTransactionModel transaction) {
        try {
            // 使用反射设置字段值
            setField(bo, "txid", transaction.getTransactionHash());
            setField(bo, "blockheight", convertBlockNumber(transaction.getBlockNumber()));
            setField(bo, "address", transaction.getToAddress());
            setField(bo, "fromaddress", transaction.getFromAddress());
            setField(bo, "contract", transaction.getContractAddress());
            setField(bo, "amount", transaction.getAmount());
            setField(bo, "fee", transaction.getTransactionFee());
            setField(bo, "timestamp", transaction.getTimestamp());
            setField(bo, "type", transaction.getTransactionType());
            setField(bo, "issync", transaction.getProcessStatus());
            setField(bo, "remark", buildRemark(transaction));
        } catch (Exception e) {
            log.error("填充交易字段失败: {}", e.getMessage());
            throw new RuntimeException("填充交易字段失败", e);
        }
    }

    /**
     * 使用反射设置字段值
     */
    private void setField(Object obj, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("设置字段{}失败: {}", fieldName, e.getMessage());
            // 某些字段可能不存在，这是正常的，不抛出异常
        }
    }

    /**
     * 转换区块号
     */
    private Long convertBlockNumber(BigInteger blockNumber) {
        if (blockNumber == null) {
            return null;
        }
        try {
            return blockNumber.longValue();
        } catch (Exception e) {
            log.warn("转换区块号失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 构建备注信息
     */
    private String buildRemark(EvmTransactionModel transaction) {
        StringBuilder remark = new StringBuilder();

        // 添加链信息
        if (transaction.getChainName() != null) {
            remark.append("链: ").append(transaction.getChainName());
        }

        // 添加代币信息
        if (transaction.getTokenSymbol() != null) {
            if (!remark.isEmpty()) {
                remark.append(", ");
            }
            remark.append("代币: ").append(transaction.getTokenSymbol());
        }

        // 添加错误信息
        if (transaction.getErrorMessage() != null && !transaction.getErrorMessage().trim().isEmpty()) {
            if (!remark.isEmpty()) {
                remark.append(", ");
            }
            remark.append("错误: ").append(transaction.getErrorMessage());
        }

        return !remark.isEmpty() ? remark.toString() : null;
    }

    /**
     * 记录性能指标
     */
    private void recordPerformanceMetrics(String chainName, int logCount, long processingTime) {
        // 这里可以集成监控系统，记录性能指标
        // 比如Micrometer、Prometheus等
        log.trace("{}链业务处理性能指标 - 日志数量: {}, 处理时间: {}ms",
            chainName, logCount, processingTime);

        // 如果处理时间过长，记录警告
        if (processingTime > 5000) { // 5秒
            log.warn("{}链业务处理耗时过长: {}ms, 日志数量: {}",
                chainName, processingTime, logCount);
        }
    }

    /**
     * 记录失败指标
     */
    private void recordFailureMetrics(String chainName, int logCount, long processingTime, Exception e) {
        // 这里可以集成监控系统，记录失败指标
        log.warn("{}链业务处理失败指标 - 日志数量: {}, 处理时间: {}ms, 异常类型: {}",
            chainName, logCount, processingTime, e.getClass().getSimpleName());
    }

    /**
     * 获取链类型
     */
    private String getChainType(TransactionModel transactionModel) {
        return transactionModel.getChainType() != null ? transactionModel.getChainType() : "UNKNOWN";
    }

    /**
     * 获取交易哈希
     */
    private String getTransactionHash(TransactionModel transactionModel) {
        if (transactionModel.getEthTransactionModel() != null &&
            transactionModel.getEthTransactionModel().getLog() != null) {
            return transactionModel.getEthTransactionModel().getLog().getTransactionHash();
        }
        return "unknown";
    }
}
