package org.dromara.wallet.wallet.monitor.solana.processor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.wallet.monitor.solana.dto.SolanaTransactionModel;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

/**
 * Solana交易处理器链
 * 
 * <p>功能：</p>
 * <ul>
 *   <li>按顺序执行所有注册的处理器</li>
 *   <li>任何处理器返回false都会中断链条</li>
 *   <li>自动按order排序处理器</li>
 *   <li>提供完整的日志记录</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaProcessorChain {

    private final List<SolanaTransactionProcessor> processors;

    /**
     * 执行处理器链
     *
     * @param transactionModel Solana交易模型
     * @param userAddress 用户指定的关联地址（可为null）
     * @return true-所有处理器都成功执行，false-某个处理器中断了链条
     */
    public boolean execute(SolanaTransactionModel transactionModel, String userAddress) {
        String signature = transactionModel.getTransactionSignature();
        
        // 按order排序处理器
        List<SolanaTransactionProcessor> sortedProcessors = processors.stream()
            .sorted(Comparator.comparingInt(SolanaTransactionProcessor::getOrder))
            .toList();

        log.debug("开始执行Solana处理器链，交易: {}, 处理器数量: {}", signature, sortedProcessors.size());

        for (SolanaTransactionProcessor processor : sortedProcessors) {
            try {
                String processorName = processor.getProcessorName();
                log.debug("执行处理器: {} (order={}), 交易: {}", processorName, processor.getOrder(), signature);

                boolean shouldContinue = processor.process(transactionModel, userAddress);
                
                if (!shouldContinue) {
                    log.info("处理器 {} 返回false，中断处理链条，交易: {}", processorName, signature);
                    return false;
                }
                
                log.debug("处理器 {} 执行成功，继续下一个处理器，交易: {}", processorName, signature);
                
            } catch (Exception e) {
                log.error("处理器 {} 执行异常，中断处理链条，交易: {}, 错误: {}", 
                    processor.getProcessorName(), signature, e.getMessage());
                return false;
            }
        }

        log.info("Solana处理器链执行完成，交易: {}", signature);
        return true;
    }

    /**
     * 获取已注册的处理器信息
     *
     * @return 处理器信息列表
     */
    public List<String> getProcessorInfo() {
        return processors.stream()
            .sorted(Comparator.comparingInt(SolanaTransactionProcessor::getOrder))
            .map(p -> String.format("%s (order=%d)", p.getProcessorName(), p.getOrder()))
            .toList();
    }
}
