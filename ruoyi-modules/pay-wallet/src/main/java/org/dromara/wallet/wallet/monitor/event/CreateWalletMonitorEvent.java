package org.dromara.wallet.wallet.monitor.event;

import lombok.Getter;
import org.dromara.wallet.domain.vo.MetaSolanaCstaddressinfoVo;
import org.springframework.context.ApplicationEvent;

/**
 * 创建钱包事件
 *
 * <AUTHOR>
 */
@Getter
public class CreateWalletMonitorEvent extends ApplicationEvent {

    private final MetaSolanaCstaddressinfoVo wallet;

    public CreateWalletMonitorEvent(Object source, MetaSolanaCstaddressinfoVo wallet) {
        super(source);
        this.wallet = wallet;
    }
}
