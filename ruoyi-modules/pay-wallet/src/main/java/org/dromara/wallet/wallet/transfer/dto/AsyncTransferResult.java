package org.dromara.wallet.wallet.transfer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 异步转账结果模型
 *
 * <p>用于封装异步转账的快速响应结果，提供以下信息：</p>
 * <ul>
 *   <li>转账订单ID，用于后续查询转账状态</li>
 *   <li>订单创建状态和时间</li>
 *   <li>异步处理的预期信息</li>
 *   <li>错误信息（如果订单创建失败）</li>
 * </ul>
 *
 * <p>设计原则：</p>
 * <ul>
 *   <li>快速响应：订单创建后立即返回，不等待区块链转账</li>
 *   <li>状态追踪：通过订单ID可查询实际转账进度</li>
 *   <li>向后兼容：与现有同步转账结果保持一致的错误处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsyncTransferResult {

    /**
     * 订单创建是否成功
     */
    private boolean success;

    /**
     * 转账订单ID
     * 成功时必填，用于查询转账状态和结果
     */
    private Long orderId;

    /**
     * 请求ID
     * 用于关联和追踪转账请求
     */
    private String requestId;

    /**
     * 区块链名称
     * 如 "TRON"、"BSC"、"SOLANA" 等
     */
    private String chainName;

    /**
     * 转账类型
     * 如 "native"、"token"、"collection" 等
     */
    private String transferType;

    /**
     * 订单创建时间
     */
    private LocalDateTime createTime;

    /**
     * 预期处理时间（分钟）
     * 给用户的预期，实际处理时间可能不同
     */
    private Integer estimatedProcessingMinutes;

    /**
     * 错误代码
     * 失败时必填，用于程序化处理错误
     */
    private String errorCode;

    /**
     * 错误消息
     * 失败时必填，用于用户展示
     */
    private String errorMessage;

    /**
     * 额外信息
     * 用于存储特定场景的额外数据
     */
    private String extraInfo;

    // ==================== 静态工厂方法 ====================

    /**
     * 创建成功结果
     */
    public static AsyncTransferResult success(Long orderId, String requestId, String chainName, String transferType) {
        return AsyncTransferResult.builder()
            .success(true)
            .orderId(orderId)
            .requestId(requestId)
            .chainName(chainName)
            .transferType(transferType)
            .createTime(LocalDateTime.now())
            .estimatedProcessingMinutes(getEstimatedProcessingTime(chainName))
            .build();
    }

    /**
     * 创建失败结果
     */
    public static AsyncTransferResult failure(String chainName, String errorCode, String errorMessage) {
        return AsyncTransferResult.builder()
            .success(false)
            .chainName(chainName)
            .errorCode(errorCode)
            .errorMessage(errorMessage)
            .createTime(LocalDateTime.now())
            .build();
    }

    /**
     * 创建失败结果 - 带异常
     */
    public static AsyncTransferResult failure(String chainName, String errorCode, String errorMessage, Exception e) {
        return AsyncTransferResult.builder()
            .success(false)
            .chainName(chainName)
            .errorCode(errorCode)
            .errorMessage(errorMessage + ": " + e.getMessage())
            .createTime(LocalDateTime.now())
            .extraInfo(e.getClass().getSimpleName())
            .build();
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取预期处理时间（分钟）
     */
    private static Integer getEstimatedProcessingTime(String chainName) {
        if (chainName == null) {
            return 5; // 默认5分钟
        }
        
        return switch (chainName.toUpperCase()) {
            case "TRON" -> 3;      // TRON相对较快
            case "BSC" -> 2;       // BSC最快
            case "ARB", "BASE" -> 3; // L2链较快
            case "ETH" -> 10;      // ETH较慢
            case "SOLANA" -> 2;    // Solana很快
            default -> 5;          // 默认5分钟
        };
    }

    /**
     * 获取格式化的状态描述
     */
    public String getStatusDescription() {
        if (success) {
            return String.format("转账订单已创建，订单ID: %d，预计%d分钟内完成处理", 
                orderId, estimatedProcessingMinutes);
        } else {
            return String.format("订单创建失败: %s", errorMessage);
        }
    }

    /**
     * 检查是否可以查询状态
     */
    public boolean canQueryStatus() {
        return success && orderId != null;
    }
}
