package org.dromara.wallet.wallet.transfer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 转账业务类型枚举
 *
 * <p>定义转账的业务用途分类，用于业务逻辑路由和事件处理</p>
 *
 * <p>设计原则：</p>
 * <ul>
 *   <li>业务语义明确：每个类型对应具体的业务场景</li>
 *   <li>扩展性良好：便于添加新的业务类型</li>
 *   <li>向后兼容：保持现有业务逻辑不变</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Getter
@AllArgsConstructor
public enum TransferBusinessType {

    /**
     * 提现转账
     * 用户从平台提取资金到外部地址
     */
    WITHDRAW("withdraw", "提现转账", "用户提取资金到外部地址"),

    /**
     * 充值转账
     * 用户向平台充值资金
     */
    DEPOSIT("deposit", "充值转账", "用户向平台充值资金"),

    /**
     * 归集转账
     * 将分散在多个地址的资金归集到主地址
     */
    COLLECT("collect", "归集转账", "资金归集到主地址"),

    /**
     * 普通转账
     * 一般性的转账操作，默认类型
     */
    TRANSFER("transfer", "普通转账", "一般性转账操作"),

    /**
     * 手续费补充
     * 为地址补充手续费
     */
    FEE_SUPPLY("fee_supply", "手续费补充", "为地址补充手续费"),

    /**
     * 热钱包转冷钱包
     * 安全性转账，将资金从热钱包转移到冷钱包
     */
    HOT_TO_COLD("hot_to_cold", "热转冷", "热钱包转冷钱包"),

    /**
     * 冷钱包转热钱包
     * 流动性转账，将资金从冷钱包转移到热钱包
     */
    COLD_TO_HOT("cold_to_hot", "冷转热", "冷钱包转热钱包"),

    /**
     * 跨链桥转账
     * 跨链资产转移
     */
    BRIDGE("bridge", "跨链桥转账", "跨链资产转移"),

    /**
     * 测试转账
     * 用于测试目的的转账
     */
    TEST("test", "测试转账", "测试目的转账");

    /**
     * 业务类型代码
     */
    private final String code;

    /**
     * 业务类型名称
     */
    private final String name;

    /**
     * 业务类型描述
     */
    private final String description;

    /**
     * 根据代码获取业务类型
     *
     * @param code 业务类型代码
     * @return 业务类型枚举，如果不存在则返回null
     */
    public static TransferBusinessType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }

        String normalizedCode = code.toLowerCase().trim();
        for (TransferBusinessType type : values()) {
            if (type.code.equals(normalizedCode)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 验证业务类型代码是否有效
     *
     * @param code 业务类型代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return fromCode(code) != null;
    }

    /**
     * 获取默认业务类型
     *
     * @return 默认业务类型（普通转账）
     */
    public static TransferBusinessType getDefault() {
        return TRANSFER;
    }

    /**
     * 规范化业务类型代码
     *
     * @param code 输入的业务类型代码
     * @return 规范化后的代码，如果无效则返回默认类型的代码
     */
    public static String normalize(String code) {
        TransferBusinessType type = fromCode(code);
        return type != null ? type.code : getDefault().code;
    }

    /**
     * 是否为提现类型
     *
     * @param code 业务类型代码
     * @return 是否为提现
     */
    public static boolean isWithdraw(String code) {
        return WITHDRAW.code.equals(code);
    }

    /**
     * 是否为充值类型
     *
     * @param code 业务类型代码
     * @return 是否为充值
     */
    public static boolean isDeposit(String code) {
        return DEPOSIT.code.equals(code);
    }

    /**
     * 是否为归集类型
     *
     * @param code 业务类型代码
     * @return 是否为归集
     */
    public static boolean isCollect(String code) {
        return COLLECT.code.equals(code);
    }

    /**
     * 是否为资金安全相关的转账类型
     *
     * @param code 业务类型代码
     * @return 是否为安全转账
     */
    public static boolean isSecurityTransfer(String code) {
        return HOT_TO_COLD.code.equals(code) || COLD_TO_HOT.code.equals(code);
    }

    /**
     * 是否为系统内部转账类型
     *
     * @param code 业务类型代码
     * @return 是否为内部转账
     */
    public static boolean isInternalTransfer(String code) {
        return COLLECT.code.equals(code) || 
               FEE_SUPPLY.code.equals(code) || 
               isSecurityTransfer(code);
    }
}
