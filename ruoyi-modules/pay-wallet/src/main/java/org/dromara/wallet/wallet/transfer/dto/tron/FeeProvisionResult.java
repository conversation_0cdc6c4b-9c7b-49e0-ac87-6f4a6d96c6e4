package org.dromara.wallet.wallet.transfer.dto.tron;

import java.math.BigDecimal;

/**
 * TRON手续费提供结果
 * 
 * <p>统一的TRON手续费提供结果数据结构，记录手续费提供的详细信息</p>
 * <p>从TronHelper迁移并统一管理，避免重复定义</p>
 * <p>支持多种手续费提供策略：第三方API、TRX转账等</p>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public class FeeProvisionResult {
    
    private final boolean provisionUsed;
    private final boolean thirdPartyApiUsed;
    private final boolean trxTransferUsed;
    private final BigDecimal provisionAmount;
    private final String provisionType;
    
    /**
     * 构造手续费提供结果
     * 
     * @param provisionUsed    是否使用了手续费提供
     * @param thirdPartyApiUsed 是否使用了第三方API
     * @param trxTransferUsed  是否使用了TRX转账
     * @param provisionAmount  提供的手续费数量
     * @param provisionType    提供类型描述
     */
    public FeeProvisionResult(boolean provisionUsed, boolean thirdPartyApiUsed,
                            boolean trxTransferUsed, BigDecimal provisionAmount, String provisionType) {
        this.provisionUsed = provisionUsed;
        this.thirdPartyApiUsed = thirdPartyApiUsed;
        this.trxTransferUsed = trxTransferUsed;
        this.provisionAmount = provisionAmount != null ? provisionAmount : BigDecimal.ZERO;
        this.provisionType = provisionType != null ? provisionType : "无";
    }
    
    // ==================== Getters ====================
    
    public boolean isProvisionUsed() { 
        return provisionUsed; 
    }
    
    public boolean isThirdPartyApiUsed() { 
        return thirdPartyApiUsed; 
    }
    
    public boolean isTrxTransferUsed() { 
        return trxTransferUsed; 
    }
    
    public BigDecimal getProvisionAmount() { 
        return provisionAmount; 
    }
    
    public String getProvisionType() { 
        return provisionType; 
    }
    
    // ==================== 静态工厂方法 ====================
    
    /**
     * 创建无需手续费提供的结果
     * 
     * @return 无手续费提供的结果
     */
    public static FeeProvisionResult noProvision() {
        return new FeeProvisionResult(false, false, false, BigDecimal.ZERO, "无");
    }
    
    /**
     * 创建第三方API提供手续费的结果
     * 
     * @param amount 提供的手续费数量
     * @return 第三方API提供的结果
     */
    public static FeeProvisionResult thirdPartyApi(BigDecimal amount) {
        return new FeeProvisionResult(true, true, false, amount, "第三方API");
    }
    
    /**
     * 创建TRX转账提供手续费的结果
     * 
     * @param amount 提供的TRX数量
     * @return TRX转账提供的结果
     */
    public static FeeProvisionResult trxTransfer(BigDecimal amount) {
        return new FeeProvisionResult(true, false, true, amount, "TRX转账");
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 获取手续费提供摘要
     * 
     * @return 手续费提供的可读描述
     */
    public String getSummary() {
        if (!provisionUsed) {
            return "用户余额充足，无需提供手续费";
        }
        
        return String.format("手续费提供: %s, 金额: %s TRX", provisionType, provisionAmount);
    }
    
    /**
     * 检查是否成功提供了手续费
     * 
     * @return 如果成功提供手续费返回true
     */
    public boolean isSuccessful() {
        return !provisionUsed || (provisionAmount.compareTo(BigDecimal.ZERO) > 0);
    }
    
    @Override
    public String toString() {
        return String.format("FeeProvisionResult{type='%s', amount=%s, used=%s}", 
            provisionType, provisionAmount, provisionUsed);
    }
}
