package org.dromara.wallet.wallet.transfer.enums;

import lombok.Getter;

/**
 * 统一交易状态枚举
 *
 * <p>定义跨链转账的统一交易状态，用于标准化不同区块链的交易状态表示</p>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Getter
public enum TransactionStatus {

    /**
     * 交易已发送到区块链网络
     * 但尚未被确认或包含在区块中
     */
    PENDING("pending", "待确认", "交易已发送，等待区块链确认"),

    /**
     * 交易已被确认并成功执行
     * 已包含在区块中且执行成功
     */
    CONFIRMED("confirmed", "已确认", "交易已成功确认并执行"),

    /**
     * 交易执行失败
     * 可能是由于Gas不足、合约执行失败等原因
     */
    FAILED("failed", "执行失败", "交易执行失败"),

    /**
     * 交易被拒绝
     * 通常是由于参数错误、余额不足等原因在发送阶段被拒绝
     */
    REJECTED("rejected", "被拒绝", "交易被区块链网络拒绝"),

    /**
     * 交易超时
     * 在指定时间内未能获得确认
     */
    TIMEOUT("timeout", "确认超时", "交易确认超时"),

    /**
     * 交易状态未知
     * 无法确定交易的当前状态
     */
    UNKNOWN("unknown", "状态未知", "无法确定交易状态"),

    /**
     * 交易已被替换
     * 通常发生在EVM链中，由于Gas价格过低被新交易替换
     */
    REPLACED("replaced", "已被替换", "交易已被更高Gas价格的交易替换"),

    /**
     * 交易已被丢弃
     * 从内存池中被移除，通常是由于网络拥堵或其他原因
     */
    DROPPED("dropped", "已丢弃", "交易已从内存池中丢弃");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 中文名称
     */
    private final String name;

    /**
     * 状态描述
     */
    private final String description;

    TransactionStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取状态
     */
    public static TransactionStatus fromCode(String code) {
        if (code == null) {
            return UNKNOWN;
        }
        for (TransactionStatus status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == CONFIRMED;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailure() {
        return this == FAILED || this == REJECTED || this == DROPPED;
    }

    /**
     * 判断是否为最终状态（不会再变化）
     */
    public boolean isFinal() {
        return this != PENDING && this != UNKNOWN;
    }

    /**
     * 判断是否需要继续等待
     */
    public boolean shouldContinueWaiting() {
        return this == PENDING || this == UNKNOWN;
    }

    /**
     * 获取状态的严重程度（用于日志级别）
     * 0: DEBUG, 1: INFO, 2: WARN, 3: ERROR
     */
    public int getSeverityLevel() {
        return switch (this) {
            case PENDING, UNKNOWN -> 0; // DEBUG
            case CONFIRMED -> 1; // INFO
            case TIMEOUT, REPLACED -> 2; // WARN
            case FAILED, REJECTED, DROPPED -> 3; // ERROR
        };
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", name, code);
    }
}
