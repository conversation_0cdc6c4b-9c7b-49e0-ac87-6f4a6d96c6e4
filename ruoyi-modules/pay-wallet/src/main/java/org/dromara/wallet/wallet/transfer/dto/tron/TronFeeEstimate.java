package org.dromara.wallet.wallet.transfer.dto.tron;

import java.math.BigDecimal;

/**
 * TRON手续费预估结果
 * 
 * <p>统一的TRON手续费预估数据结构，用于封装手续费计算的详细信息</p>
 * <p>从TronHelper迁移并统一管理，避免重复定义</p>
 *
 * @param energyNeeded    需要的能量数量
 * @param bandwidthNeeded 需要的带宽数量  
 * @param trxNeeded       需要的TRX数量（可读格式，以TRX为单位）
 * 
 * <AUTHOR>
 * @date 2025/7/19
 */
public record TronFeeEstimate(
    long energyNeeded, 
    long bandwidthNeeded, 
    BigDecimal trxNeeded
) {
    
    /**
     * 创建TRX转账的手续费预估（只需要带宽）
     * 
     * @param bandwidthNeeded 需要的带宽数量
     * @param trxNeeded       需要的TRX数量
     * @return TRX转账手续费预估
     */
    public static TronFeeEstimate forTrxTransfer(long bandwidthNeeded, BigDecimal trxNeeded) {
        return new TronFeeEstimate(0, bandwidthNeeded, trxNeeded);
    }
    
    /**
     * 创建TRC20转账的手续费预估（需要能量和带宽）
     * 
     * @param energyNeeded    需要的能量数量
     * @param bandwidthNeeded 需要的带宽数量
     * @param trxNeeded       需要的TRX数量
     * @return TRC20转账手续费预估
     */
    public static TronFeeEstimate forTrc20Transfer(long energyNeeded, long bandwidthNeeded, BigDecimal trxNeeded) {
        return new TronFeeEstimate(energyNeeded, bandwidthNeeded, trxNeeded);
    }
    
    /**
     * 检查是否需要手续费
     * 
     * @return 如果需要TRX手续费返回true
     */
    public boolean requiresFee() {
        return trxNeeded != null && trxNeeded.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * 检查是否为TRX转账（不需要能量）
     * 
     * @return 如果是TRX转账返回true
     */
    public boolean isTrxTransfer() {
        return energyNeeded == 0;
    }
    
    /**
     * 获取手续费预估摘要
     * 
     * @return 手续费预估的可读描述
     */
    public String getSummary() {
        if (isTrxTransfer()) {
            return String.format("TRX转账: 带宽=%d, 费用=%s TRX", bandwidthNeeded, trxNeeded);
        } else {
            return String.format("TRC20转账: 能量=%d, 带宽=%d, 费用=%s TRX", 
                energyNeeded, bandwidthNeeded, trxNeeded);
        }
    }
}
