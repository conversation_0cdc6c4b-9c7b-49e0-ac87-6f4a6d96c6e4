package org.dromara.wallet.wallet.monitor.solana.dto;

import lombok.Getter;
import lombok.Setter;
import org.dromara.wallet.domain.bo.MetaSolanaTransactionBo;
import org.p2p.solanaj.rpc.types.Block;
import org.p2p.solanaj.rpc.types.ConfirmedTransaction;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Solana交易模型
 *
 * <AUTHOR>
 * @date 2025/4/13 21:55
 **/
@Getter
@Setter
public class SolanaTransactionModel {
    /**
     * 区块信息
     */
    private Block solBlock;

    /**
     * 交易对象
     */
    private ConfirmedTransaction transactionObject;

    /**
     * 地址余额变化映射
     * Key: 地址, Value: Map<代币mint地址, 余额变化>
     * 用于处理器链间传递数据
     */
    private Map<String, Map<String, BigDecimal>> addressChanges;

    /**
     * 解析后的交易业务对象列表
     * 用于处理器链间传递数据
     */
    private List<MetaSolanaTransactionBo> parsedTransactions;

    /**
     * 获取交易签名
     */
    public String getTransactionSignature() {
        if (transactionObject != null &&
            transactionObject.getTransaction() != null &&
            transactionObject.getTransaction().getSignatures() != null &&
            !transactionObject.getTransaction().getSignatures().isEmpty()) {
            return transactionObject.getTransaction().getSignatures().get(0);
        }
        return null;
    }

    public static SolTransactionModelBuilder builder() {
        return new SolTransactionModelBuilder();
    }

    public static class SolTransactionModelBuilder {
        private SolanaTransactionModel solanaTransactionModel = new SolanaTransactionModel();

        public SolTransactionModelBuilder setSolBlock(Block solBlock) {
            solanaTransactionModel.solBlock = solBlock;
            return this;
        }

        public SolTransactionModelBuilder setTransactionObject(ConfirmedTransaction transactionObject) {
            solanaTransactionModel.transactionObject = transactionObject;
            return this;
        }

        public SolTransactionModelBuilder setAddressChanges(Map<String, Map<String, BigDecimal>> addressChanges) {
            solanaTransactionModel.addressChanges = addressChanges;
            return this;
        }

        public SolTransactionModelBuilder setParsedTransactions(List<MetaSolanaTransactionBo> parsedTransactions) {
            solanaTransactionModel.parsedTransactions = parsedTransactions;
            return this;
        }

        public SolanaTransactionModel build() {
            return solanaTransactionModel;
        }
    }
}
