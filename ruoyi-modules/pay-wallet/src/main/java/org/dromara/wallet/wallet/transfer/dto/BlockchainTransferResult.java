package org.dromara.wallet.wallet.transfer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.wallet.wallet.transfer.dto.TransactionConfirmationResult;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 统一区块链转账结果模型
 *
 * <p>用于封装跨链转账的统一返回结果，提供以下信息：</p>
 * <ul>
 *   <li>转账成功/失败状态</li>
 *   <li>交易哈希和区块链信息</li>
 *   <li>手续费使用情况</li>
 *   <li>详细的错误信息（如果失败）</li>
 *   <li>执行时间和性能统计</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlockchainTransferResult {

    /**
     * 转账是否成功
     */
    private boolean success;

    /**
     * 交易哈希
     * 成功时必填，用于查询交易详情
     */
    private String txHash;

    /**
     * 区块链名称
     * 如 "TRON"、"BSC"、"SOLANA" 等
     */
    private String chainName;

    /**
     * 是否使用了手续费钱包
     */
    private boolean feeProvided;

    /**
     * 手续费金额
     * 如果使用了手续费钱包，记录提供的手续费数量
     */
    private BigDecimal feeAmount;

    /**
     * 手续费代币符号
     * 如 "TRX"、"BNB"、"SOL" 等
     */
    private String feeTokenSymbol;

    /**
     * 错误代码
     * 失败时必填，用于程序化处理错误
     */
    private String errorCode;

    /**
     * 错误信息
     * 失败时必填，用于显示给用户
     */
    private String errorMessage;

    /**
     * 执行开始时间
     */
    @Builder.Default
    private LocalDateTime startTime = LocalDateTime.now();

    /**
     * 执行结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long executionTimeMs;

    /**
     * 转账金额（原始请求）
     */
    private BigDecimal transferAmount;

    /**
     * 转账代币符号（原始请求）
     */
    private String transferTokenSymbol;

    /**
     * 发送方地址
     */
    private String fromAddress;

    /**
     * 接收方地址
     */
    private String toAddress;

    /**
     * 区块确认数
     * 如果等待了确认，记录确认的区块数
     */
    private Integer confirmations;

    /**
     * 是否等待了确认
     */
    private boolean waitedForConfirmation;

    /**
     * 详细的确认结果
     * 包含确认状态、确认时间、确认数等详细信息
     */
    private TransactionConfirmationResult confirmationResult;

    /**
     * 确认时间
     * 交易确认完成的时间
     */
    private LocalDateTime confirmationTime;

    /**
     * 请求ID（用于追踪）
     */
    private String requestId;

    /**
     * 转账记录ID
     * 关联到meta_transfer_rec表的主键ID
     */
    private Long transferRecordId;

    /**
     * 额外信息
     * 用于存储链特定的额外信息
     */
    private String extraInfo;

    // ==================== 静态工厂方法 ====================

    /**
     * 创建成功结果 - 简单版本
     */
    public static BlockchainTransferResult success(String txHash, String chainName) {
        return BlockchainTransferResult.builder()
            .success(true)
            .txHash(txHash)
            .chainName(chainName)
            .endTime(LocalDateTime.now())
            .build();
    }

    /**
     * 创建成功结果 - 包含手续费信息
     */
    public static BlockchainTransferResult successWithFee(String txHash, String chainName,
                                                         boolean feeProvided, BigDecimal feeAmount,
                                                         String feeTokenSymbol) {
        return BlockchainTransferResult.builder()
            .success(true)
            .txHash(txHash)
            .chainName(chainName)
            .feeProvided(feeProvided)
            .feeAmount(feeAmount)
            .feeTokenSymbol(feeTokenSymbol)
            .endTime(LocalDateTime.now())
            .build();
    }

    /**
     * 创建失败结果
     */
    public static BlockchainTransferResult failure(String chainName, String errorCode, String errorMessage) {
        return BlockchainTransferResult.builder()
            .success(false)
            .chainName(chainName)
            .errorCode(errorCode)
            .errorMessage(errorMessage)
            .endTime(LocalDateTime.now())
            .build();
    }

    /**
     * 从现有的BlockchainTransferSuccess转换
     * 用于适配现有的Helper类返回值
     */
    public static BlockchainTransferResult fromSuccess(org.dromara.wallet.wallet.transfer.dto.BlockchainTransferSuccess success) {
        return BlockchainTransferResult.builder()
            .success(true)
            .txHash(success.transactionHash())
            .chainName(success.chainName())
            .feeProvided(success.feeWalletUsed())
            .feeAmount(success.feeProvided())
            .feeTokenSymbol(success.getNativeTokenSymbol())
            .endTime(LocalDateTime.now())
            .build();
    }

    // ==================== 便捷方法 ====================

    /**
     * 计算执行耗时
     */
    public void calculateExecutionTime() {
        if (startTime != null && endTime != null) {
            executionTimeMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 设置结束时间并计算耗时
     */
    public void markCompleted() {
        this.endTime = LocalDateTime.now();
        calculateExecutionTime();
    }

    /**
     * 检查是否为超时错误
     */
    public boolean isTimeoutError() {
        return !success && "TIMEOUT".equals(errorCode);
    }

    /**
     * 检查是否为余额不足错误
     */
    public boolean isInsufficientBalanceError() {
        return !success && ("INSUFFICIENT_BALANCE".equals(errorCode) ||
                           "INSUFFICIENT_FEE".equals(errorCode));
    }

    /**
     * 检查是否为网络错误
     */
    public boolean isNetworkError() {
        return !success && ("NETWORK_ERROR".equals(errorCode) ||
                           "RPC_ERROR".equals(errorCode) ||
                           "CONNECTION_TIMEOUT".equals(errorCode));
    }

    /**
     * 获取简要状态描述
     */
    public String getStatusDescription() {
        if (success) {
            StringBuilder sb = new StringBuilder("转账成功");
            if (feeProvided && feeAmount != null) {
                sb.append("，手续费: ").append(feeAmount).append(" ").append(feeTokenSymbol);
            }
            if (executionTimeMs != null) {
                sb.append("，耗时: ").append(executionTimeMs).append("ms");
            }
            return sb.toString();
        } else {
            return "转账失败: " + (errorMessage != null ? errorMessage : errorCode);
        }
    }

    /**
     * 获取完整的结果摘要
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("链: ").append(chainName);
        sb.append(", 状态: ").append(success ? "成功" : "失败");

        if (success) {
            sb.append(", 交易哈希: ").append(txHash);
            if (feeProvided) {
                sb.append(", 手续费: ").append(feeAmount).append(" ").append(feeTokenSymbol);
            }
        } else {
            sb.append(", 错误: ").append(errorMessage);
        }

        if (executionTimeMs != null) {
            sb.append(", 耗时: ").append(executionTimeMs).append("ms");
        }

        return sb.toString();
    }

    // ==================== 常用错误代码常量 ====================

    public static final String ERROR_INVALID_PARAMS = "INVALID_PARAMS";
    public static final String ERROR_INSUFFICIENT_BALANCE = "INSUFFICIENT_BALANCE";
    public static final String ERROR_INSUFFICIENT_FEE = "INSUFFICIENT_FEE";
    public static final String ERROR_NETWORK_ERROR = "NETWORK_ERROR";
    public static final String ERROR_RPC_ERROR = "RPC_ERROR";
    public static final String ERROR_TIMEOUT = "TIMEOUT";
    public static final String ERROR_TRANSACTION_FAILED = "TRANSACTION_FAILED";
    public static final String ERROR_CONFIRMATION_FAILED = "CONFIRMATION_FAILED";
    public static final String ERROR_UNSUPPORTED_CHAIN = "UNSUPPORTED_CHAIN";
    public static final String ERROR_UNSUPPORTED_TOKEN = "UNSUPPORTED_TOKEN";
    public static final String ERROR_TRANSFER_FAILED = "TRANSFER_FAILED";
}
