package org.dromara.wallet.wallet.monitor.solana;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.config.solana.SolanaRpcConfig;
import org.dromara.wallet.config.solana.SolanaMonitorConfig;
import org.dromara.wallet.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.wallet.domain.vo.MonitorStatusVo;
import org.dromara.wallet.domain.vo.SubscriptionInfoVo;
import org.dromara.wallet.config.SolanaCoinType;
import org.dromara.wallet.wallet.monitor.event.CreateWalletMonitorEvent;
import org.dromara.wallet.wallet.monitor.solana.event.TransactionEvent;
import org.dromara.wallet.service.IMetaSolanaCstaddressinfoService;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.time.LocalDateTime;
import java.time.Instant;
import java.time.ZoneId;


/**
 * Solana监控服务 - 带异步订阅和自动重连
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SolanaMonitorManager implements ApplicationRunner {

    private final SolanaRpcConfig rpcConfig;
    private final SolanaMonitorConfig monitorConfig;
    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;
    private final ApplicationEventPublisher eventPublisher;

    // WebSocket客户端
    private WebSocketClient webSocketClient;

    // JSON处理器
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 连接状态
    private volatile boolean connected = false;

    // 订阅状态统计
    private volatile int totalAddresses = 0;
    private volatile AtomicInteger subscribedAddresses = new AtomicInteger(0);

    /**
     * 订阅统计结果类
     */
    @Data
    public static class SubscriptionStats {
        private final String connectionId;
        private final int totalWallets;
        private final int totalAddresses;
        private final int successfulSubscriptions;
        private final int failedSubscriptions;
        private final long startTime;
        private final long endTime;
        private final Map<String, Integer> coinTypeStats = new HashMap<>();
        private final List<String> failedAddresses = new ArrayList<>();

        public SubscriptionStats(String connectionId, int totalWallets, int totalAddresses) {
            this.connectionId = connectionId;
            this.totalWallets = totalWallets;
            this.totalAddresses = totalAddresses;
            this.successfulSubscriptions = 0;
            this.failedSubscriptions = 0;
            this.startTime = System.currentTimeMillis();
            this.endTime = startTime;
        }

        private SubscriptionStats(String connectionId, int totalWallets, int totalAddresses,
                                  int successfulSubscriptions, int failedSubscriptions,
                                  long startTime, long endTime, Map<String, Integer> coinTypeStats,
                                  List<String> failedAddresses) {
            this.connectionId = connectionId;
            this.totalWallets = totalWallets;
            this.totalAddresses = totalAddresses;
            this.successfulSubscriptions = successfulSubscriptions;
            this.failedSubscriptions = failedSubscriptions;
            this.startTime = startTime;
            this.endTime = endTime;
            this.coinTypeStats.putAll(coinTypeStats);
            this.failedAddresses.addAll(failedAddresses);
        }

        public SubscriptionStats withSuccess(String coinType) {
            Map<String, Integer> newCoinTypeStats = new HashMap<>(this.coinTypeStats);
            newCoinTypeStats.merge(coinType, 1, Integer::sum);
            return new SubscriptionStats(connectionId, totalWallets, totalAddresses,
                successfulSubscriptions + 1, failedSubscriptions, startTime,
                System.currentTimeMillis(), newCoinTypeStats, failedAddresses);
        }

        public SubscriptionStats withFailure(String address, String coinType) {
            Map<String, Integer> newCoinTypeStats = new HashMap<>(this.coinTypeStats);
            List<String> newFailedAddresses = new ArrayList<>(this.failedAddresses);
            newFailedAddresses.add(address + "(" + coinType + ")");
            return new SubscriptionStats(connectionId, totalWallets, totalAddresses,
                successfulSubscriptions, failedSubscriptions + 1, startTime,
                System.currentTimeMillis(), newCoinTypeStats, newFailedAddresses);
        }

        public String getSummary() {
            long duration = endTime - startTime;
            double successRate = totalAddresses > 0 ? (double) successfulSubscriptions / totalAddresses * 100 : 0;

            StringBuilder sb = new StringBuilder();
            sb.append(String.format("订阅完成 [连接ID:%s] - ", connectionId));
            sb.append(String.format("钱包:%d个, 地址:%d个, 成功:%d个, 失败:%d个, ",
                totalWallets, totalAddresses, successfulSubscriptions, failedSubscriptions));
            sb.append(String.format("成功率:%.1f%%, 耗时:%dms", successRate, duration));

            if (!coinTypeStats.isEmpty()) {
                sb.append(" [按币种: ");
                coinTypeStats.forEach((coinType, count) ->
                    sb.append(String.format("%s=%d ", coinType, count)));
                sb.append("]");
            }

            return sb.toString();
        }

        public String getDetailedSummary() {
            StringBuilder sb = new StringBuilder(getSummary());
            if (!failedAddresses.isEmpty()) {
                sb.append("\n  失败地址: ").append(failedAddresses);
            }
            return sb.toString();
        }

        // Getters
        public boolean hasFailures() {
            return failedSubscriptions > 0;
        }
    }

    // 当前订阅统计
    private volatile SubscriptionStats currentStats;

    /**
     * 交易统计类
     */
    public static class TransactionStats {
        private final Map<String, Integer> addressTransactionCount = new ConcurrentHashMap<>();
        private final AtomicInteger totalTransactions = new AtomicInteger(0);
        private volatile long lastReportTime = System.currentTimeMillis();

        public void recordTransaction(String address) {
            addressTransactionCount.merge(address, 1, Integer::sum);
            totalTransactions.incrementAndGet();
        }

        public boolean shouldReport() {
            // 1分钟报告一次
            long reportInterval = 60000;
            return System.currentTimeMillis() - lastReportTime >= reportInterval;
        }

        public String getStatsAndReset() {
            long currentTime = System.currentTimeMillis();
            long duration = currentTime - lastReportTime;
            lastReportTime = currentTime;

            int total = totalTransactions.getAndSet(0);
            Map<String, Integer> currentCounts = new HashMap<>(addressTransactionCount);
            addressTransactionCount.clear();

            if (total == 0) {
                return null; // 没有交易，不输出日志
            }

            StringBuilder sb = new StringBuilder();
            sb.append(String.format("交易统计报告 - 总计:%d笔, 时间段:%.1f分钟", total, duration / 60000.0));

            if (currentCounts.size() <= 10) {
                // 地址数量较少时，显示详细信息
                sb.append(" [详情: ");
                currentCounts.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .forEach(entry -> sb.append(String.format("%s=%d笔 ",
                        entry.getKey().substring(Math.max(0, entry.getKey().length() - 8)),
                        entry.getValue())));
                sb.append("]");
            } else {
                // 地址数量较多时，只显示汇总信息
                sb.append(String.format(" [涉及地址:%d个]", currentCounts.size()));
            }

            return sb.toString();
        }

        // 添加getter方法用于状态查询
        public int getTotalTransactions() {
            return totalTransactions.get();
        }

        public long getLastReportTime() {
            return lastReportTime;
        }

        public int getAddressCount() {
            return addressTransactionCount.size();
        }
    }

    // 交易统计实例
    private final TransactionStats transactionStats = new TransactionStats();

    // 专用的订阅线程池
    private final ExecutorService subscriptionExecutor =
        Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "solana-subscription");
            t.setDaemon(true);
            return t;
        });

    // 专用的连接线程池
    private final ExecutorService connectionExecutor =
        Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "solana-connection");
            t.setDaemon(true);
            return t;
        });

    // 重连调度器
    private final ScheduledExecutorService reconnectScheduler =
        Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "solana-reconnect");
            t.setDaemon(true);
            return t;
        });

    // 异步订阅管理
    private volatile Future<?> currentSubscriptionTask;
    private volatile boolean shouldStopSubscription = false;
    private volatile long connectionId = 0;
    private volatile boolean isReconnecting = false;

    // 重连控制
    private final AtomicInteger reconnectAttempts = new AtomicInteger(0);

    // 状态同步锁
    private final Object stateLock = new Object();

    // 订阅映射：订阅ID -> 地址
    private final Map<String, String> subscriptionMap = new ConcurrentHashMap<>();

    // 监控地址集合
    private final Set<String> monitorAddresses = ConcurrentHashMap.newKeySet();

    // 待确认的订阅请求：requestId -> CompletableFuture<Boolean>
    private final Map<String, CompletableFuture<Boolean>> pendingSubscriptions = new ConcurrentHashMap<>();

    // 订阅确认超时时间（秒）
    private static final int SUBSCRIPTION_TIMEOUT = 10;

    @PostConstruct
    public void init() {
        log.info("Solana监控服务初始化完成（极简版本）");
    }

    @Override
    public void run(ApplicationArguments args) {
        setTenant();

        // 检查监控开关
        if (!monitorConfig.isEnabled()) {
            log.info("Solana监控功能已禁用，跳过启动");
            return;
        }

        try {
            log.info("Solana监控服务开始启动...");
            loadMonitorAddresses();

            // 异步启动连接，不阻塞系统启动
            startConnectionAsync();

            log.info("Solana监控服务启动完成，WebSocket连接将在后台异步建立");

        } catch (Exception e) {
            log.error("监控服务启动失败", e);
            // 即使启动失败，也尝试异步重连
            startConnectionAsync();
        }
    }

    /**
     * 加载需要监控的地址
     */
    private void loadMonitorAddresses() {
        try {
            List<MetaSolanaCstaddressinfoVo> wallets = solanaCstaddressinfoService.queryAll();

            for (MetaSolanaCstaddressinfoVo wallet : wallets) {
                if (wallet.getCstAddress() != null && !wallet.getCstAddress().isEmpty()) {
                    monitorAddresses.add(wallet.getCstAddress());
                }
                if (wallet.getCstUsdtAddress() != null && !wallet.getCstUsdtAddress().isEmpty()) {
                    monitorAddresses.add(wallet.getCstUsdtAddress());
                }
                if (wallet.getCstUsdcAddress() != null && !wallet.getCstUsdcAddress().isEmpty()) {
                    monitorAddresses.add(wallet.getCstUsdcAddress());
                }
            }

            log.info("加载了{}个监控地址", monitorAddresses.size());

        } catch (Exception e) {
            log.error("加载监控地址失败", e);
        }
    }

    /**
     * 初始化WebSocket连接
     */
    private void initializeConnection() {
        try {
            String websocketUrl = rpcConfig.getWebsocketUrl();
            if (websocketUrl == null || websocketUrl.trim().isEmpty()) {
                throw new IllegalStateException("WebSocket URL未配置");
            }

            webSocketClient = new WebSocketClient(new URI(websocketUrl)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    synchronized (stateLock) {
                        connectionId = System.currentTimeMillis();
                        connected = true;
                        isReconnecting = false;

                        // 重置重连计数
                        reconnectAttempts.set(0);

                        log.info("WebSocket连接已建立，连接ID: {}, 将开始订阅{}个地址", connectionId, monitorAddresses.size());

                        // 连接建立后立即开始异步订阅
                        startAsyncSubscription();
                    }
                }

                @Override
                public void onMessage(String message) {
                    handleMessage(message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.info("WebSocket连接关闭 [代码: {}, 原因: {}, 远程: {}]", code, reason, remote);

                    boolean shouldReconnect = false;

                    synchronized (stateLock) {
                        boolean wasConnected = connected;
                        connected = false;

                        // 立即停止订阅
                        stopSubscription();

                        // 清理订阅映射
                        subscriptionMap.clear();

                        // 清理待确认的订阅请求
                        for (CompletableFuture<Boolean> future : pendingSubscriptions.values()) {
                            future.complete(false);
                        }
                        pendingSubscriptions.clear();

                        // 如果之前是连接状态且不是主动关闭，启动重连
                        if (wasConnected && !isReconnecting && reconnectAttempts.get() < monitorConfig.getMaxReconnectAttempts()) {
                            shouldReconnect = true;
                        }
                    }

                    if (shouldReconnect) {
                        scheduleReconnection();
                    } else if (reconnectAttempts.get() >= monitorConfig.getMaxReconnectAttempts()) {
                        log.error("已达到最大重连次数({})，停止重连", monitorConfig.getMaxReconnectAttempts());
                    }
                }

                @Override
                public void onError(Exception ex) {
                    log.error("WebSocket发生错误", ex);
                    connected = false;
                }
            };

            webSocketClient.connect();
            log.info("正在连接WebSocket: {}", websocketUrl);

        } catch (Exception e) {
            log.error("初始化WebSocket连接失败", e);
            throw new RuntimeException("初始化连接失败", e);
        }
    }

    /**
     * 处理WebSocket消息
     */
    private void handleMessage(String message) {
        if (message == null || message.isEmpty()) {
            return;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(message);

            if (rootNode.has("result")) {
                // 处理订阅确认
                handleSubscriptionConfirmation(rootNode);
            } else {
                // 处理交易消息
                handleTransactionMessage(rootNode);
            }

        } catch (JsonProcessingException e) {
            log.error("解析JSON消息失败: {}", e.getMessage());
        } catch (Exception e) {
            log.error("处理消息时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 处理订阅确认消息
     */
    private void handleSubscriptionConfirmation(JsonNode rootNode) {
        try {
            // 1. 连接状态检查
            if (!connected) {
                log.debug("连接已断开，忽略订阅确认");
                return;
            }

            String requestId = rootNode.get("id").asText();
            // 2. 连接ID验证
            if (!requestId.startsWith(connectionId + "_")) {
                log.debug("忽略旧连接的订阅确认: {}", requestId);
                return;
            }

            String subscriptionId = rootNode.get("result").asText();

            // 从请求ID中解析地址信息
            String address = parseAddressFromRequestId(requestId);
            if (address != null) {
                subscriptionMap.put(subscriptionId, address);
                subscribedAddresses.incrementAndGet(); // 增加已订阅计数

                // 更新统计信息
                String coinType = parseCoinTypeFromRequestId(requestId);
                if (currentStats != null) {
                    currentStats = currentStats.withSuccess(coinType);
                }

                // 只在debug级别显示单个地址订阅成功
                log.debug("地址{}订阅成功，订阅ID: {}，币种: {}，进度: {}/{}",
                    address, subscriptionId, coinType, subscribedAddresses, totalAddresses);

                // 完成对应的等待Future
                CompletableFuture<Boolean> future = pendingSubscriptions.remove(requestId);
                if (future != null) {
                    future.complete(true);
                }
            }

        } catch (Exception e) {
            log.error("处理订阅确认消息失败", e);

            // 如果处理失败，也要完成Future避免一直等待
            String requestId;
            try {
                requestId = rootNode.get("id").asText();
                CompletableFuture<Boolean> future = pendingSubscriptions.remove(requestId);
                if (future != null) {
                    future.complete(false);
                }
            } catch (Exception ex) {
                log.warn("无法解析requestId用于完成Future");
            }
        }
    }

    /**
     * 处理交易消息
     */
    private void handleTransactionMessage(JsonNode rootNode) {
        try {
            String signature = rootNode.at("/params/result/value/signature").asText(null);
            if (signature != null && !signature.isEmpty()) {
                String subscription = rootNode.at("/params/subscription").asText(null);
                String address = subscriptionMap.get(subscription);

                if (address != null) {
                    // 记录交易统计
                    transactionStats.recordTransaction(address);

                    // 只在debug级别显示单笔交易详情
                    log.debug("收到交易：签名={}, 地址={}", signature, address);

                    // 检查是否需要输出统计报告
                    if (transactionStats.shouldReport()) {
                        String statsReport = transactionStats.getStatsAndReset();
                        if (statsReport != null) {
                            log.info(statsReport);
                        }
                    }

                    // 发布交易事件
                    eventPublisher.publishEvent(new TransactionEvent(this, signature, address));
                } else {
                    log.warn("未找到订阅ID对应的地址：订阅ID={}", subscription);
                }
            }
        } catch (Exception e) {
            log.error("处理交易消息失败", e);
        }
    }

    /**
     * 从请求ID解析地址
     */
    private String parseAddressFromRequestId(String requestId) {
        try {
            // 请求ID格式：connectionId_coinTypewalletId
            // 例如：1703123456789_SOL123
            int underscoreIndex = requestId.indexOf('_');
            if (underscoreIndex == -1) {
                log.warn("请求ID格式不正确，缺少下划线: {}", requestId);
                return null;
            }

            String coinAndWalletPart = requestId.substring(underscoreIndex + 1);

            // 解析币种和钱包ID
            for (SolanaCoinType coinType : SolanaCoinType.values()) {
                String coinCode = coinType.getCode();
                if (coinAndWalletPart.startsWith(coinCode)) {
                    String walletIdStr = coinAndWalletPart.substring(coinCode.length());
                    long walletId = Long.parseLong(walletIdStr);

                    MetaSolanaCstaddressinfoVo wallet = solanaCstaddressinfoService.queryById(walletId);
                    if (wallet != null) {
                        return getAddressByCoinType(wallet, coinCode);
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析地址失败，requestId: {}, 错误: {}", requestId, e.getMessage());
        }
        return null;
    }

    /**
     * 从请求ID中解析币种类型
     */
    private String parseCoinTypeFromRequestId(String requestId) {
        try {
            // 请求ID格式：connectionId_coinTypewalletId
            // 例如：1703123456789_SOL123
            int underscoreIndex = requestId.indexOf('_');
            if (underscoreIndex == -1) {
                return "UNKNOWN";
            }

            String coinTypeAndWalletId = requestId.substring(underscoreIndex + 1);

            // 解析币种
            for (SolanaCoinType coinType : SolanaCoinType.values()) {
                if (coinTypeAndWalletId.startsWith(coinType.getCode())) {
                    return coinType.getCode();
                }
            }

            return "UNKNOWN";
        } catch (Exception e) {
            log.debug("解析币种类型失败: {}, error: {}", requestId, e.getMessage());
            return "UNKNOWN";
        }
    }

    /**
     * 根据币种获取地址
     */
    private String getAddressByCoinType(MetaSolanaCstaddressinfoVo wallet, String coinType) {
        return switch (coinType) {
            case "SOL" -> wallet.getCstAddress();
            case "USDT" -> wallet.getCstUsdtAddress();
            case "USDC" -> wallet.getCstUsdcAddress();
            default -> null;
        };
    }

    // 重连机制（带退避策略）
    private void scheduleReconnection() {
        synchronized (stateLock) {
            if (isReconnecting) {
                log.debug("重连已在进行中，跳过");
                return;
            }

            int currentAttempts = reconnectAttempts.get();
            if (currentAttempts >= monitorConfig.getMaxReconnectAttempts()) {
                log.error("已达到最大重连次数({})，停止重连", monitorConfig.getMaxReconnectAttempts());
                return;
            }

            isReconnecting = true;
        }

        // 指数退避策略：使用配置的初始延迟和最大延迟
        long delay = Math.min(monitorConfig.getInitialReconnectDelay() * (1L << reconnectAttempts.get()),
            monitorConfig.getMaxReconnectDelay());

        log.warn("第{}次重连将在{}秒后开始", reconnectAttempts.get() + 1, delay / 1000);

        reconnectScheduler.schedule(() -> {
            try {
                int attemptNum = reconnectAttempts.incrementAndGet();
                log.warn("尝试第{}次重新连接WebSocket...", attemptNum);
                reconnect();
            } catch (Exception e) {
                log.error("重连异常", e);
                // 重连失败，继续尝试
                synchronized (stateLock) {
                    isReconnecting = false;
                }
                scheduleReconnection();
            }
        }, delay, TimeUnit.MILLISECONDS);
    }

    // 重连实现
    private void reconnect() {
        try {
            // 关闭旧连接
            if (webSocketClient != null && webSocketClient.isOpen()) {
                webSocketClient.close();
            }

            // 创建新连接
            initializeConnection();

            // 等待连接建立
            if (waitForConnection()) {
                log.info("重连成功，连接ID: {}", connectionId);
                // 连接成功后会在onOpen中自动重置状态和开始订阅
            } else {
                log.warn("重连失败，将继续尝试");
                synchronized (stateLock) {
                    isReconnecting = false;
                }
                scheduleReconnection();
            }
        } catch (Exception e) {
            log.error("重连过程异常", e);
            synchronized (stateLock) {
                isReconnecting = false;
            }
            scheduleReconnection();
        }
    }

    /**
     * 异步启动连接
     */
    private void startConnectionAsync() {
        connectionExecutor.submit(() -> {
            try {
                log.info("开始异步建立WebSocket连接...");
                initializeConnection();

                if (waitForConnection()) {
                    log.info("WebSocket连接建立成功，订阅将在onOpen中异步启动");
                } else {
                    log.warn("WebSocket连接建立失败，将启动重连机制");
                    scheduleReconnection();
                }

            } catch (Exception e) {
                log.error("异步连接过程异常", e);
                scheduleReconnection();
            }
        });
    }

    /**
     * 等待连接建立（内部使用，不阻塞系统启动）
     */
    private boolean waitForConnection() {
        try {
            int timeout = monitorConfig.getConnectionTimeout();
            long startTime = System.currentTimeMillis();

            while (!connected && (System.currentTimeMillis() - startTime) < timeout * 1000L) {
                TimeUnit.MILLISECONDS.sleep(100);
            }

            return connected;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    // 启动异步订阅
    private void startAsyncSubscription() {
        // 停止之前的订阅任务并等待完成
        stopSubscriptionAndWait();

        shouldStopSubscription = false;
        final long currentConnId = connectionId; // 保存当前连接ID

        currentSubscriptionTask = subscriptionExecutor.submit(() -> {
            try {
                // 再次检查连接状态和ID
                if (!connected || connectionId != currentConnId) {
                    log.info("连接状态已变更，取消订阅任务");
                    return;
                }

                log.info("开始异步订阅过程，连接ID: {}", currentConnId);
                subscribeAllAddressesWithInterruption();
                log.info("异步订阅过程完成");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("订阅任务被中断");
            } catch (Exception e) {
                log.error("订阅过程异常", e);
                // 订阅异常时尝试重连
                synchronized (stateLock) {
                    if (connected && !isReconnecting) {
                        scheduleReconnection();
                    }
                }
            }
        });
    }

    // 可中断的订阅方法
    private void subscribeAllAddressesWithInterruption() throws InterruptedException {
        setTenant();

        List<MetaSolanaCstaddressinfoVo> wallets = solanaCstaddressinfoService.queryAll();

        // 计算总地址数
        int totalCount = 0;
        for (MetaSolanaCstaddressinfoVo wallet : wallets) {
            if (wallet.getCstAddress() != null && !wallet.getCstAddress().isEmpty()) totalCount++;
            if (wallet.getCstUsdtAddress() != null && !wallet.getCstUsdtAddress().isEmpty()) totalCount++;
            if (wallet.getCstUsdcAddress() != null && !wallet.getCstUsdcAddress().isEmpty()) totalCount++;
        }
        totalAddresses = totalCount;
        subscribedAddresses = new AtomicInteger(0);

        // 初始化订阅统计
        currentStats = new SubscriptionStats(String.valueOf(connectionId), wallets.size(), totalCount);
        log.info("开始批量订阅 - 连接ID:{}, 钱包:{}个, 地址:{}个", connectionId, wallets.size(), totalCount);

        int successCount = 0;

        for (MetaSolanaCstaddressinfoVo wallet : wallets) {
            // 检查中断条件
            if (shouldStopSubscription || !connected || Thread.currentThread().isInterrupted()) {
                log.info("订阅过程被中断，已处理{}个钱包", successCount);
                throw new InterruptedException("订阅过程被中断");
            }

            try {
                if (subscribeWallet(wallet)) {
                    successCount++;
                }

                // 可中断的延时
                Thread.sleep(100);

            } catch (InterruptedException e) {
                throw e; // 重新抛出中断异常
            } catch (Exception e) {
                log.warn("订阅钱包失败: {}", e.getMessage());
            }
        }

        // 输出聚合的订阅结果
        if (currentStats != null) {
            if (currentStats.hasFailures()) {
                log.warn(currentStats.getSummary());
                if (log.isDebugEnabled()) {
                    log.debug(currentStats.getDetailedSummary());
                }
            } else {
                log.info(currentStats.getSummary());
            }
        } else {
            // 兼容旧的日志格式
            log.info("地址订阅完成，服务端确认成功: {}个钱包，总地址数: {}, 成功订阅: {}",
                successCount, totalAddresses, subscribedAddresses);
        }
    }

    // 停止订阅
    private void stopSubscription() {
        shouldStopSubscription = true;

        if (currentSubscriptionTask != null && !currentSubscriptionTask.isDone()) {
            boolean cancelled = currentSubscriptionTask.cancel(true);
            log.info("订阅任务取消结果: {}", cancelled);
        }
    }

    // 停止订阅并等待完成
    private void stopSubscriptionAndWait() {
        shouldStopSubscription = true;

        if (currentSubscriptionTask != null && !currentSubscriptionTask.isDone()) {
            boolean cancelled = currentSubscriptionTask.cancel(true);
            log.info("订阅任务取消结果: {}", cancelled);

            // 等待任务真正结束
            try {
                currentSubscriptionTask.get(2, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.debug("等待订阅任务结束时异常（正常）: {}", e.getMessage());
            }
        }
    }

    /**
     * 订阅单个钱包的所有地址
     */
    private boolean subscribeWallet(MetaSolanaCstaddressinfoVo wallet) {
        boolean success = true;

        // 订阅SOL地址
        if (wallet.getCstAddress() != null && !wallet.getCstAddress().isEmpty()) {
            success &= subscribeAddress(wallet.getCstAddress(), wallet.getId(), SolanaCoinType.SOL);
        }

        // 订阅USDT地址
        if (wallet.getCstUsdtAddress() != null && !wallet.getCstUsdtAddress().isEmpty()) {
            success &= subscribeAddress(wallet.getCstUsdtAddress(), wallet.getId(), SolanaCoinType.USDT);
        }

        // 订阅USDC地址
        if (wallet.getCstUsdcAddress() != null && !wallet.getCstUsdcAddress().isEmpty()) {
            success &= subscribeAddress(wallet.getCstUsdcAddress(), wallet.getId(), SolanaCoinType.USDC);
        }

        return success;
    }

    /**
     * 订阅指定地址（同步等待确认）
     */
    private boolean subscribeAddress(String address, Long walletId, SolanaCoinType coinType) {
        if (!connected) {
            return false;
        }

        try {
            String requestId = connectionId + "_" + coinType.getCode() + walletId;
            String request = buildSubscriptionRequest(address, walletId, coinType.getCode());

            // 创建等待确认的Future
            CompletableFuture<Boolean> confirmationFuture = new CompletableFuture<>();
            pendingSubscriptions.put(requestId, confirmationFuture);

            // 发送订阅请求
            webSocketClient.send(request);

            // 等待确认（带超时）
            try {
                Boolean confirmed = confirmationFuture.get(SUBSCRIPTION_TIMEOUT, TimeUnit.SECONDS);
                if (confirmed != null && confirmed) {
                    return true;
                } else {
                    log.warn("地址{}订阅确认失败，币种: {}", address, coinType.getCode());
                    return false;
                }
            } catch (TimeoutException e) {
                log.warn("地址{}订阅确认超时({}秒)，币种: {}", address, SUBSCRIPTION_TIMEOUT, coinType.getCode());
                pendingSubscriptions.remove(requestId); // 清理超时的请求
                // 更新统计信息
                if (currentStats != null) {
                    currentStats = currentStats.withFailure(address, coinType.getCode());
                }
                return false;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.debug("地址{}订阅过程被中断，币种: {}", address, coinType.getCode());
                pendingSubscriptions.remove(requestId);
                // 更新统计信息
                if (currentStats != null) {
                    currentStats = currentStats.withFailure(address, coinType.getCode());
                }
                return false;
            } catch (ExecutionException e) {
                log.debug("地址{}订阅过程异常，币种: {}, error: {}", address, coinType.getCode(), e.getMessage());
                pendingSubscriptions.remove(requestId);
                // 更新统计信息
                if (currentStats != null) {
                    currentStats = currentStats.withFailure(address, coinType.getCode());
                }
                return false;
            }

        } catch (Exception e) {
            log.error("订阅地址{}失败", address, e);
            return false;
        }
    }

    /**
     * 构建订阅请求
     */
    private String buildSubscriptionRequest(String address, Long walletId, String coinType) {
        Map<String, Object> params = Map.of(
            "jsonrpc", "2.0",
            "id", connectionId + "_" + coinType + walletId, // 包含连接ID
            "method", "logsSubscribe",
            "params", Arrays.asList(
                Map.of("mentions", Collections.singletonList(address)),
                Map.of("commitment", "finalized")
            )
        );
        return JsonUtils.toJsonString(params);
    }

    /**
     * 添加监控地址
     */
    public void addMonitorAddress(MetaSolanaCstaddressinfoVo wallet) {
        // 如果监控被禁用，直接返回
        if (!monitorConfig.isEnabled()) {
            log.debug("监控功能已禁用，跳过添加监控地址");
            return;
        }

        try {
            // 添加到监控集合
            if (wallet.getCstAddress() != null) {
                monitorAddresses.add(wallet.getCstAddress());
            }
            if (wallet.getCstUsdtAddress() != null) {
                monitorAddresses.add(wallet.getCstUsdtAddress());
            }
            if (wallet.getCstUsdcAddress() != null) {
                monitorAddresses.add(wallet.getCstUsdcAddress());
            }

            // 如果已连接，立即订阅
            if (connected) {
                subscribeWallet(wallet);
            }

        } catch (Exception e) {
            log.error("添加监控地址失败", e);
        }
    }

    /**
     * 处理钱包监控事件
     */
    @EventListener
    public void handleWalletMonitorEvent(CreateWalletMonitorEvent event) {
        addMonitorAddress(event.getWallet());
    }

    // ============ 监控状态管理接口 ============

    /**
     * 获取监控状态信息
     */
    public MonitorStatusVo getMonitorStatus() {
        MonitorStatusVo.MonitorStatusVoBuilder builder = MonitorStatusVo.builder()
            .connected(connected)
            .connectionId(connectionId)
            .monitorEnabled(monitorConfig.isEnabled())
            .totalAddresses(monitorAddresses.size())
            .subscribedAddresses(subscribedAddresses.get())
            .reconnectAttempts(reconnectAttempts.get())
            .isReconnecting(isReconnecting);

        // 设置连接时间
        if (connected && connectionId > 0) {
            builder.connectionTime(LocalDateTime.ofInstant(
                Instant.ofEpochMilli(connectionId), ZoneId.systemDefault()));
        }

        // 设置交易统计
        builder.transactionStats(MonitorStatusVo.TransactionStatsVo.builder()
            .totalTransactions((long) transactionStats.getTotalTransactions())
            .todayTransactions((long) transactionStats.getTotalTransactions()) // 简化实现，使用总数
            .lastTransactionTime(transactionStats.getLastReportTime() > 0 ?
                LocalDateTime.ofInstant(Instant.ofEpochMilli(transactionStats.getLastReportTime()),
                    ZoneId.systemDefault()) : null)
            .build());

        // 设置最后订阅统计
        if (currentStats != null) {
            builder.lastSubscriptionStats(MonitorStatusVo.SubscriptionStatsVo.builder()
                .connectionId(currentStats.getConnectionId())
                .totalWallets(currentStats.getTotalWallets())
                .totalAddresses(currentStats.getTotalAddresses())
                .successfulSubscriptions(currentStats.getSuccessfulSubscriptions())
                .failedSubscriptions(currentStats.getFailedSubscriptions())
                .startTime(LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(currentStats.getStartTime()), ZoneId.systemDefault()))
                .endTime(LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(currentStats.getEndTime()), ZoneId.systemDefault()))
                .coinTypeStats(new HashMap<>(currentStats.getCoinTypeStats()))
                .build());
        }

        return builder.build();
    }

    /**
     * 获取订阅信息列表
     */
    public List<SubscriptionInfoVo> getSubscriptions() {
        List<SubscriptionInfoVo> subscriptions = new ArrayList<>();

        for (Map.Entry<String, String> entry : subscriptionMap.entrySet()) {
            String subscriptionId = entry.getKey();
            String address = entry.getValue();

            subscriptions.add(SubscriptionInfoVo.builder()
                .subscriptionId(subscriptionId)
                .address(address)
                .status(connected ? "ACTIVE" : "INACTIVE")
                .connectionId(connectionId)
                .active(connected)
                .subscriptionTime(connected && connectionId > 0 ?
                    LocalDateTime.ofInstant(Instant.ofEpochMilli(connectionId), ZoneId.systemDefault()) : null)
                .lastActivityTime(LocalDateTime.now())
                .build());
        }

        return subscriptions;
    }

    /**
     * 手动触发重连
     */
    public boolean triggerReconnect() {
        if (isReconnecting) {
            log.warn("重连已在进行中，忽略手动重连请求");
            return false;
        }

        log.info("收到手动重连请求，开始重连...");
        synchronized (stateLock) {
            if (!isReconnecting) {
                isReconnecting = true;
                scheduleReconnection();
                return true;
            }
        }
        return false;
    }
    @PreDestroy
    public void cleanup() {
        try {
            log.info("开始清理SolanaMonitorService...");

            synchronized (stateLock) {
                // 停止重连
                isReconnecting = false;

                // 停止订阅
                stopSubscription();

                // 清理待确认的订阅请求
                for (CompletableFuture<Boolean> future : pendingSubscriptions.values()) {
                    future.complete(false);
                }
                pendingSubscriptions.clear();

                // 断开连接
                connected = false;
            }

            // 关闭WebSocket连接
            if (webSocketClient != null && webSocketClient.isOpen()) {
                webSocketClient.close();
            }

            // 关闭线程池
            subscriptionExecutor.shutdown();
            reconnectScheduler.shutdown();

            // 等待线程池关闭
            if (!subscriptionExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warn("订阅线程池未在5秒内关闭，强制关闭");
                subscriptionExecutor.shutdownNow();
            }
            if (!reconnectScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warn("重连线程池未在5秒内关闭，强制关闭");
                reconnectScheduler.shutdownNow();
            }

            log.info("SolanaMonitorService清理完成");
        } catch (Exception e) {
            log.error("清理过程中发生错误", e);
        }
    }

    private static void setTenant() {
        TenantHelper.setDynamic("000000", true);
    }
}
