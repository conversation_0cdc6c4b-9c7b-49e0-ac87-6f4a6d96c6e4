package org.dromara.wallet.wallet.transfer.dto;

import java.math.BigDecimal;

/**
 * Solana手续费估算实现
 * 
 * <p>封装Solana转账的手续费估算信息，包括：</p>
 * <ul>
 *   <li>交易签名费用</li>
 *   <li>账户租金（如需要）</li>
 *   <li>等价SOL需求量</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public record SolanaFeeEstimate(
    long signatureFee,
    long accountRent,
    BigDecimal solNeeded
) implements FeeEstimate {

    @Override
    public BigDecimal getNativeTokenNeeded() {
        return solNeeded;
    }

    @Override
    public String getNativeTokenSymbol() {
        return "SOL";
    }

    @Override
    public String getDescription() {
        return String.format("Signature Fee: %d lamports, Account Rent: %d lamports, SOL: %s", 
            signatureFee, accountRent, solNeeded);
    }

    /**
     * 创建SOL转账的手续费估算
     */
    public static SolanaFeeEstimate forSolTransfer(long signatureFee, BigDecimal solNeeded) {
        return new SolanaFeeEstimate(signatureFee, 0, solNeeded);
    }

    /**
     * 创建SPL代币转账的手续费估算
     */
    public static SolanaFeeEstimate forSplTokenTransfer(long signatureFee, long accountRent, BigDecimal solNeeded) {
        return new SolanaFeeEstimate(signatureFee, accountRent, solNeeded);
    }

    /**
     * 获取总费用（lamports）
     */
    public long getTotalFeeInLamports() {
        return signatureFee + accountRent;
    }
}
