package org.dromara.wallet.wallet.transfer.dto;

import java.math.BigDecimal;

/**
 * 区块链转账成功结果记录
 *
 * <p>用于封装Helper类返回的转账成功信息</p>
 * <p>这是一个Record类，提供不可变的数据结构</p>
 *
 * @param transactionHash    交易哈希
 * @param chainName         区块链名称
 * @param feeWalletUsed     是否使用了手续费钱包
 * @param feeProvided       提供的手续费金额
 * @param nativeTokenSymbol 原生代币符号
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public record BlockchainTransferSuccess(
    String transactionHash,
    String chainName,
    boolean feeWalletUsed,
    BigDecimal feeProvided,
    String nativeTokenSymbol
) {

    /**
     * 获取原生代币符号
     * 提供向后兼容的方法名
     */
    public String getNativeTokenSymbol() {
        return nativeTokenSymbol;
    }

    /**
     * 创建成功结果 - 不使用手续费钱包
     */
    public static BlockchainTransferSuccess success(String transactionHash, String chainName) {
        return new BlockchainTransferSuccess(
            transactionHash,
            chainName,
            false,
            BigDecimal.ZERO,
            null
        );
    }

    /**
     * 创建成功结果 - 使用手续费钱包
     */
    public static BlockchainTransferSuccess successWithFee(String transactionHash, String chainName,
                                                          BigDecimal feeProvided, String nativeTokenSymbol) {
        return new BlockchainTransferSuccess(
            transactionHash,
            chainName,
            true,
            feeProvided,
            nativeTokenSymbol
        );
    }

    /**
     * 创建简单成功结果 - 兼容Helper类调用
     */
    public static BlockchainTransferSuccess simple(String transactionHash, String chainName) {
        return new BlockchainTransferSuccess(
            transactionHash,
            chainName,
            false,
            BigDecimal.ZERO,
            null
        );
    }

    /**
     * 创建带手续费的成功结果 - 兼容Helper类调用
     */
    public static BlockchainTransferSuccess withFee(String transactionHash, boolean feeWalletUsed,
                                                   BigDecimal feeProvided, String nativeTokenSymbol) {
        return new BlockchainTransferSuccess(
            transactionHash,
            null, // chainName 由调用方设置
            feeWalletUsed,
            feeProvided,
            nativeTokenSymbol
        );
    }

    /**
     * 检查是否完成 - 兼容Helper类调用
     * Record类型的对象创建后就是完成状态
     */
    public boolean isCompleted() {
        return transactionHash != null && !transactionHash.trim().isEmpty();
    }
}
