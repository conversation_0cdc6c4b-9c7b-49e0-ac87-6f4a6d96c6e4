package org.dromara.wallet.api.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/18 14:49
 **/
@Data
public class RemoteWalletDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private BigDecimal amount;
    private String toAddress;
    private String tokenSymbol;
    private String chainName;
    private String requestId;
}
